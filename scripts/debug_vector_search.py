import numpy as np
from pathlib import Path
import sys

sys.path.append(str(Path(__file__).resolve().parents[1]))  # 把项目根目录加入 PYTHONPATH

from database.models import DatabaseManager
from utils.ai_utils import ai_client


db = DatabaseManager()
video_id = 1
sentence = "立刻去大使馆"

vec = ai_client.get_embedding(sentence)
print(f"[DEBUG] 查询向量维度: {vec.shape if vec is not None else 'None'}")

# 【修改】添加空值检查
if vec is None:
    print("错误：无法生成查询向量，搜索中止。")
    exit(1)

scene_ids = {10, 11, 12, 13}  # 手动填入场景 shot id
res = db.search_shots_by_semantic(video_id, vec, top_k=15, allowed_shot_ids=scene_ids)

print(f"\n=== 语义搜索结果详情 (共 {len(res)} 个) ===")
for i, r in enumerate(res, 1):
    emb = np.frombuffer(r["text_embedding_vector"], dtype=np.float32)
    print(f"\n--- 排名 {i}: Shot {r['shot_order_id']} ---")
    print(f"相似度: {r['similarity']:.4f}")
    print(f"向量维度: {emb.shape}")
    print(f"时间段: {r['start_time']:.2f}s - {r['end_time']:.2f}s (时长: {r['end_time'] - r['start_time']:.2f}s)")
    print(f"场景编号: {r.get('scene_number', '未知')}")
    print(
        f"视觉描述: {r.get('visual_description', '')[0:100]}{'...' if len(r.get('visual_description', '')) > 100 else ''}"
    )
    print(f"动作描述: {r.get('action', '')[0:100]}{'...' if len(r.get('action', '')) > 100 else ''}")
    print(f"人物: {r.get('people', '') or '无'}")
    print(f"场所: {r.get('setting', '') or '无'}")
    print(f"对话: {r.get('dialogue', '') or '无对话'}")
    print(f"是否有对话: {'是' if r.get('has_dialogue') else '否'}")
    characters_present = r.get("characters_present") or []
    print(f"在场角色: {', '.join(map(str, characters_present)) or '无'}")
    print(f"文件路径: {r.get('clip_url', '未知')}")
