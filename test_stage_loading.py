#!/usr/bin/env python3
"""
测试Stage类加载
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append('/Users/<USER>/projects/auto-cutter')

try:
    print("正在测试 EnhancedBaseStage...")
    from utils.enhanced_base_stage import EnhancedBaseStage
    print("✅ EnhancedBaseStage 加载成功")
    
    print("正在测试 Stage11Reader...")
    from stages.stage_d2s_reader import Stage11Reader
    print("✅ Stage11Reader 加载成功")
    
    print("正在测试 Stage12ReaderCausalLink...")
    from stages.stage_d2s_reader_causal import Stage12ReaderCausalLink  
    print("✅ Stage12ReaderCausalLink 加载成功")
    
    print("\n所有Stage类加载成功! 🎉")

except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()