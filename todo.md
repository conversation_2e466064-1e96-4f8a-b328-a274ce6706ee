# 待办事项 (To-Do List)

## 核心功能迭代 (基于D2S设计文档) - **已完成**

- [x] **实现D2S基础层:**
  - [x] **创建Stage8Foundation:** 实现从设计文档和数据库分析数据中，生成主JSON上下文文件。

- [x] **实现因果图谱构建 (Reader模块):**
  - [x] **事件识别:** 让AI扫描主JSON文件，识别出潜在的“叙事事件”。
  - [x] **因果链接推断:** 让AI基于事件列表和项目设定，推断事件间的因果关系。
  - [x] **图谱组装与精炼:** 实现图谱的自动化构建和逻辑修正（破除逻辑循环）。

- [x] **重构剧本生成流程 (Rewriter模块):**
  - [x] **大纲生成:** 实现从因果图谱到分场景故事大纲的转换。
  - [x] **剧本生成:** 实现基于大纲的剧本生成，包含对白、动作描述和对素材的`clip_id`引用。

- [x] **增强人机回环 (HITL) 机制:**
  - [x] **图谱审查节点:** 开发一个界面或流程，允许用户审查和修改生成的因果图谱。
  - [x] **剧本修订指令:** 实现通过自然语言指令来修改剧本的功能（例如，“让这段对白更紧张”）。

## 长期技术探索 - **已完成**

- [x] **向量化与语义搜索:**
  - [x] 实现分镜头描述、场景描述的向量化存储。
  - [x] 实现基于自然语言的视频素材语义搜索。

- [x] **角色情感弧线分析:**
  - [x] 在叙事结构中增加对角色情感变化的追踪和分析。

## 项目当前状态总结

我们的系统现在已经从一个初步的自动化工具，演进为一个拥有**“总体规划”**能力的、真正智能的叙事创作引擎。

1.  **分层叙事规划 (已完成)**:
    *   **宏观策略优先**: 我们成功地引入了`阶段13`（策略规划）。现在，AI会先像一个总编剧（Showrunner）一样，为整个故事制定一份包含**全局旁白风格、节奏规划、关键时刻标记**的“创作策略文档”。
    *   **微观执行有据**: `阶段14`（剧本创作）现在会严格遵循这份策略文档，为每个场景生成既符合场景目标、又服务于全局节奏的剧本内容，从根本上解决了“生硬拼接”的问题。

2.  **可执行的剪辑蓝图 (已完成)**:
    *   `阶段14`生成的剧本现在包含了**纯视觉叙事段落**的能力。AI能够智能地判断何时应该让画面“自己说话”，并为这些段落精确地关联多个分镜头素材。
    *   `阶段16`（成片合成）也已升级，能够完全理解这种新剧本结构，正确处理旁白段落和纯视觉段落，生成节奏感更强的最终剪辑项目文件。

3.  **代码质量与健壮性 (已提升)**:
    *   我们全面采纳了受目标API支持的、最可靠的结构化数据生成方式。
    *   我们对所有阶段的编号和依赖关系进行了全面审查和修正，确保了工作流的逻辑严谨性。
    *   我们为`README.md`添加了清晰的开发规范，为未来的维护和扩展奠定了良好基础。
