services:
  btcnode:
    image: kylemanna/bitcoind
    container_name: btcnode
    ports:
      - 127.0.0.1:8333:8333
      - 127.0.0.1:8332:8332
    volumes:
      - ./btc-slowpart:/bitcoin/.bitcoin
      - ./btc-fastpart/chainstate:/bitcoin/.bitcoin/chainstate
      - ./btc-fastpart/indexes:/bitcoin/.bitcoin/indexes

  mempool:
    container_name: mempool
    image: nginx:latest
    environment:
      - VIRTUAL_HOST=memhot.tinyidealink.xyz
      - LETSENCRYPT_HOST=memhot.tinyidealink.xyz
      - VIRTUAL_PORT=8000
    volumes:
      - ./mempool.conf:/etc/nginx/conf.d/default.conf

  atomicals:
    image: lucky2077/atomicals-electrumx:v1.5.0.3
    container_name: atomicals
    restart: always
    ports:
      - 127.0.0.1:50001:50001
      - 127.0.0.1:50002:50002
      - 127.0.0.1:50004:50004
      - 127.0.0.1:37020:8000
      - 127.0.0.1:37021:8080
    volumes:
      - ./atomicals:/data
    environment:
      #- DAEMON_URL=nextdao:nextdao@btc:8332
      - DAEMON_URL=electrumx:tl3X7RWxH90FQbDaDiNENwXJQv6foZUT@btcnode:8332
      - COIN=BitCoin
      - VIRTUAL_HOST=hot.tinyidealink.xyz
      - LETSENCRYPT_HOST=hot.tinyidealink.xyz
      - VIRTUAL_PORT=8080
        #- LOG_LEVEL=ERROR
    healthcheck:
      test: "nc -z localhost 50001"
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  nginx-proxy:
    image: nginxproxy/nginx-proxy
    container_name: nginx-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - /var/run/docker.sock:/tmp/docker.sock:ro
      - ./certs:/etc/nginx/certs:rw
      - vhost:/etc/nginx/vhost.d
      - html:/usr/share/nginx/html

  nginx-proxy-acme:
    image: nginxproxy/acme-companion
    container_name: nginx-proxy-acme
    depends_on:
      - nginx-proxy
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./certs:/etc/nginx/certs:rw
      - vhost:/etc/nginx/vhost.d
      - html:/usr/share/nginx/html
      - acme:/etc/acme.sh
    environment:
      - NGINX_PROXY_CONTAINER=nginx-proxy
      - DEFAULT_EMAIL=<EMAIL>

volumes:
  vhost:
  html:
  acme:
