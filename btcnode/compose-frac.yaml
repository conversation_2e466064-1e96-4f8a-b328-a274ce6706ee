  ord:
    image: ordinals/ord
    container_name: ord
    environment:
      - VIRTUAL_HOST=ordmilk.tinyidealink.xyz
      - LETSENCRYPT_HOST=ordmilk.tinyidealink.xyz
      - VIRTUAL_PORT=80
    volumes:
      - ./btcnode:/root/.bitcoin
      - /root/btcnode/chainstate:/root/.bitcoin/chainstate
      - /root/btcnode/indexes:/root/.bitcoin/indexes
      - ./ord.yaml:/app/ord.yaml
      - ./ord-data:/data


  redis:
    image: redis:latest
    container_name: redis
    restart: always
    ports:
      - "48531:48531"
    environment:
      - VIRTUAL_HOST=kiwi.tinyidealink.xyz
      - LETSENCRYPT_HOST=kiwi.tinyidealink.xyz
    volumes:
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
      - ./redis/tests/tls:/etc/ssl
    command: redis-server /usr/local/etc/redis/redis.conf
