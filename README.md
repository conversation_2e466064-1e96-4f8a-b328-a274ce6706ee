# AutoCutter

## 📖 项目结构与贡献规范

为了保持项目的可维护性和可扩展性，所有处理阶段（Stage）的实现都应遵循以下规范：

- **文件命名**: 所有阶段的实现都应放在 `stages/` 目录下。文件名应清晰地描述该阶段的功能，**不应包含阶段编号**。例如：`stage_analysis.py`, `stage_audio_transcription.py`。
- **阶段编号**: 每个阶段的逻辑编号由其类内部的 `@property def stage_number(self) -> int:` 方法唯一确定。这允许我们在不修改文件名的情况下，灵活地调整和插入新的处理阶段。
- **依赖关系**: 每个阶段必须在其 `check_prerequisites` 方法中，明确声明它所依赖的前置阶段。

---

# 基于数据库驱动与深度分析的智能视频剪辑工作流

本项目实现了一个完整的自动化视频剪辑解决方案。它通过对视频进行多层次的深度分析，将海量结构化数据高效存入 PostgreSQL 数据库。结合音频转写、人脸识别、外部知识检索，利用 AI
生成富有洞察力的叙事大纲，再依据大纲从数据库中精确检索素材、生成文案并最终合成精华短视频。

---

## 📖 核心流程：雪花写作法驱动的"数据到剧本"工作流

本项目实现了一个名为"数据到剧本"（Data-to-Script, D2S）的端到端工作流。它借鉴了经典的"雪花写作法"（Snowflake Method），从一个简单的核心概念（Tagline）开始，通过结构化的、层层递进的方式，将海量资产（视频、文档）
AI 的创造力结合，最终生成高质量、叙事连贯的视频剧本。

工作流被划分为三个核心部分：

### **第一部分：基础层 - 资产解构与数据化 (阶段 1-10)**

此阶段的目标是将用户拥有的异构资产集合（视频、设计文档）转化为一个统一的、机器可读的格式，并生成故事的"核心凝结核"。

- **阶段 1-9**: 对视频进行多层次的深度分析，包括镜头分割、视觉分析、音频转写、角色识别、场景聚合等。
- **阶段 10 (数据基础构建)**: 结合所有已有数据和用户的宏观要求，AI 自动生成一份详细的"创作说明书"（Creative Brief），作为后续所有AI创作的"唯一真实之源"。

### **第二部分：生成核心 - 从大纲到剧本 (阶段 11-19)**

此阶段采用先进的"读者-重写者"（Reader-Rewriter）叙事合成范式，将结构化数据和故事核心，层层扩展为完整的剧本。

- **阶段 11-13 (读者模块 - 逻辑骨架)**:
  1.  **事件识别**: AI 扫描素材库，识别出所有潜在的"叙事事件"。
  2.  **因果推断**: AI 基于项目设定，推断事件间的因果关系。
  3.  **图谱精炼**: 自动构建并修正因果图谱，确保故事逻辑自洽。

- **阶段 14-21 (重写者与剪辑模块 - 扩展与创作)**:
  1.  **详细大纲生成 (阶段 14)**: 将逻辑清晰的因果图谱，扩展为一份包含详细段落描述的分场景故事大纲。
  2.  **角色档案生成 (阶段 15)**: 为主要角色创建包含动机、目标、冲突和角色弧线的详细档案。
  3.  **创作策略规划 (阶段 16)**: AI 扮演总编剧，为整个剧本制定宏观的创作策略，包括开篇、高潮和结尾的叙事手法。
  4.  **故事流编排 (阶段 17)**: AI 扮演导演，根据宏观策略，从完整大纲中挑选并重新排序场景，形成最终的、具有戏剧张力的故事流。
  5.  **视觉剧本组装 (阶段 18)**: AI 扮演“视觉序列汇编器”，将叙事单元忠实地翻译成结构化的、纯视觉的剧本节拍。
  6.  **AI精剪 (阶段 19)**: AI 扮演“剪辑师”，为每个视觉节拍从可用素材中挑选出最匹配的镜头序列。
  7.  **【新】时长控制 (阶段 20)**: AI 扮演“时长剪辑师”，根据目标时长，通过迭代压缩算法精确控制成片总长度。
  8.  **【新】成片合成 (阶段 21)**: 智能解析**最终版**的剧本，组装时间线，并导出可供专业剪辑软件使用的项目文件（FCP7 XML）。

### **第三部分：人机回环与评估 - 迭代、精炼与控制 (内置与交互命令)**

此阶段将 D2S 工作流从一个"黑箱式"的生成器，转变为一个强大的、可交互的创作工具，确保 AI 的效率与人类的创造力有机结合。

- **全局评估 (内置于阶段 18)**: 在剧本创作的最后一步，AI 会依据`REACT-S`框架对生成的完整剧本进行一次自动化的、结构化的全局质量评估，并将报告存入数据库。
- **交互式命令**:
  - `name`: 允许用户审查和修正 AI 的角色识别结果。
  - `review_graph`: 提供交互式界面，让用户在剧本创作前审查和修改故事的逻辑骨架（因果图谱）。
  - `refine_script`: 提供交互式界面，让用户通过自然语言指令对 AI 生成的剧本进行逐句精修 (在阶段 18 或 19 完成后使用)。

## 🚀 快速开始

### 3. 运行流程

```bash
# 从输入目录导入新视频
./main.py import

# 列出所有视频
./main.py list

# 查看视频ID为1的状态 (使用 -v 标志)
./main.py status -v 1

# 运行视频1的阶段1 (可使用 -v 1 -s 1)
./main.py run --video 1 --stage 1

# 完全强制重新运行视频1的阶段1 (删除所有缓存和片段文件)
./main.py run -v 1 -s 1 --force full

# 软强制重新运行视频1的阶段1 (保留已分割的镜头片段，仅重新进行AI分析和人脸提取)
# 如果镜头分析已完成，此命令将仅重新提取人脸，非常高效。
./main.py run -v 1 -s 1 --force soft

# 仅重新生成视频1阶段1中所有镜头的文本向量 (不重新分析视频)
./main.py run -v 1 -s 1 --force vectorize

# 从阶段3(外部资料增强)开始，运行视频1的后续所有阶段
./main.py run --video 1 --stage all --from 3

# 为视频1的阶段2指定一个外部字幕文件，跳过自动转写
./main.py run -v 1 -s 2 --subtitles /path/to/your/video.srt

# ...并跳过前15.5秒的字幕条目
./main.py run -v 1 -s 2 --subtitles /path/to/your/video.srt --skip-until 15.5

# 为视频1启动交互式因果图谱审查界面 (在阶段13完成后使用)
./main.py review_graph -v 1

# 为视频1启动交互式剧本修订界面 (在阶段17完成后使用)
./main.py refine_script -v 1

# 为视频1执行语义搜索 (在阶段1完成后使用)
./main.py search -v 1 -q "一个男人在雨中奔跑" -k 3

# 为视频1启动交互式角色命名界面 (在阶段6完成后使用)
./main.py name -v 1
```

---

## 部署与性能建议

- **GPU 依赖**: 为了获得最佳性能，强烈建议在配备NVIDIA GPU的机器上运行本项目。以下组件将从GPU中受益匪浅：
  - `demucs` (人声分离)
  - `facenet-pytorch` (人脸检测与嵌入)
  - `mlx-whisper` (语音转写，在Apple Silicon上使用MPS)
- **API 成本**: 本项目依赖外部LLM API (如火山方舟、OpenAI等)，请注意相关服务的API调用成本和速率限制。
- **存储空间**: 视频处理会生成大量的中间文件（镜头片段、音频文件等），请确保有足够的磁盘空间。

## 最终渲染

本工作流的最终产出是一个 `.xml` 项目文件，而非直接的 `.mp4` 视频。您需要将此 XML 文件导入到专业的非线性编辑软件（NLE）中进行最终检查、微调和渲染导出。

支持的软件包括：
- **Final Cut Pro**
- **Adobe Premiere Pro**
- **DaVinci Resolve**

导入后，您会看到一个包含多条视频轨道和音频轨道的时间线，所有素材和旁白都已按剧本排布好。您可以在此基础上进行颜色校正、添加转场特效、调整音量等操作，然后导出最终的视频文件。

---

## 🛠 技术栈

- **视频处理：** `PySceneDetect`, `OpenCV`, `ffmpeg`
- **AI 能力：**
  - **人声分离:** `demucs`
  - **语音活动检测:** `silero-vad`
  - **人脸识别:** `facenet-pytorch`, `scikit-learn`
  - **语音转文字:** `mlx-whisper`
  - **语言/视觉模型:** 可配置的 LLM API (如 OpenAI, 火山方舟)
  - **网络检索:** `Tavily API`
- **数据库：** `PostgreSQL` + `SQLAlchemy` + `Alembic`
- **依赖管理：** `uv`
