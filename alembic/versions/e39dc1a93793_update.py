"""update

Revision ID: e39dc1a93793
Revises: 50fbf1a95d56
Create Date: 2025-07-11 21:10:33.044372

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'e39dc1a93793'
down_revision = '50fbf1a95d56'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('detected_shots',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('video_id', sa.Integer(), nullable=False),
    sa.Column('start_time', sa.Float(), nullable=False),
    sa.Column('end_time', sa.Float(), nullable=False),
    sa.Column('start_frame', sa.Integer(), nullable=False),
    sa.Column('end_frame', sa.Integer(), nullable=False),
    sa.Column('detection_params', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['video_id'], ['video_meta.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.drop_table('detected_scenes')
    op.add_column('face_embeddings', sa.Column('time_offset_in_shot', sa.Float(), nullable=False))
    op.drop_column('face_embeddings', 'time_offset_in_scene')
    op.alter_column('research', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('research', 'status',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.drop_constraint(op.f('scenes_sequence_id_fkey'), 'scenes', type_='foreignkey')
    op.create_foreign_key(None, 'scenes', 'story_sequences', ['sequence_id'], ['id'], ondelete='SET NULL')
    op.alter_column('shots', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    op.alter_column('shots', 'status',
               existing_type=sa.VARCHAR(),
               nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('shots', 'status',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('shots', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.drop_constraint(None, 'scenes', type_='foreignkey')
    op.create_foreign_key(op.f('scenes_sequence_id_fkey'), 'scenes', 'story_sequences', ['sequence_id'], ['id'], ondelete='CASCADE')
    op.alter_column('research', 'status',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('research', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.add_column('face_embeddings', sa.Column('time_offset_in_scene', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=False))
    op.drop_column('face_embeddings', 'time_offset_in_shot')
    op.create_table('detected_scenes',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('video_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('start_time', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=False),
    sa.Column('end_time', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=False),
    sa.Column('start_frame', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('end_frame', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('detection_params', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['video_id'], ['video_meta.id'], name=op.f('detected_scenes_video_id_fkey'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('detected_scenes_pkey'))
    )
    op.drop_table('detected_shots')
    # ### end Alembic commands ###
