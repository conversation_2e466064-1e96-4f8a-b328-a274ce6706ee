"""Add character emotional arcs and script evaluation table

Revision ID: 9443758feedc
Revises: 36aed358cd4b
Create Date: 2025-07-16 14:30:10.353606

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9443758feedc'
down_revision = '36aed358cd4b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('script_evaluations',
    sa.<PERSON>umn('id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('video_id', sa.Integer(), nullable=False),
    sa.Column('evaluation_data', sa.JSON(), nullable=False),
    sa.<PERSON>umn('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['video_id'], ['video_meta.id'], ondelete='CASCADE'),
    sa.<PERSON>KeyConstraint('id'),
    sa.UniqueConstraint('video_id')
    )
    op.add_column('story_sequences', sa.<PERSON>umn('character_emotional_arcs', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('story_sequences', 'character_emotional_arcs')
    op.drop_table('script_evaluations')
    # ### end Alembic commands ###
