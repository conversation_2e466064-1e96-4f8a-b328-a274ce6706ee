"""Remove file_path from stage_outputs table

Revision ID: 30920c9e7125
Revises: 0722d8345a54
Create Date: 2025-07-18 23:10:29.150014

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '30920c9e7125'
down_revision = '0722d8345a54'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('stage_outputs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('video_id', sa.Integer(), nullable=False),
    sa.Column('stage_number', sa.Integer(), nullable=False),
    sa.Column('stage_name', sa.String(), nullable=False),
    sa.Column('output_type', sa.String(), nullable=False),
    sa.Column('output_data', sa.J<PERSON>(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['video_id'], ['video_meta.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('video_id', 'stage_number', 'output_type', name='uq_video_stage_output')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('stage_outputs')
    # ### end Alembic commands ###
