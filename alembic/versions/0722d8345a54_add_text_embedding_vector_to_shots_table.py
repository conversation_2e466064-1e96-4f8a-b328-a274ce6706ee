"""Add text_embedding_vector to Shots table

Revision ID: 0722d8345a54
Revises: b8dc8fa70485
Create Date: 2025-07-18 10:14:56.184468

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0722d8345a54'
down_revision = 'b8dc8fa70485'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('shots', sa.Column('text_embedding_vector', sa.LargeBinary(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('shots', 'text_embedding_vector')
    # ### end Alembic commands ###
