"""Add has_dialogue to Shots table

Revision ID: b6601dc7289e
Revises: 9443758feedc
Create Date: 2025-07-17 18:15:33.225941

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'b6601dc7289e'
down_revision = '9443758feedc'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('shots', sa.<PERSON>umn('has_dialogue', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('shots', 'has_dialogue')
    # ### end Alembic commands ###
