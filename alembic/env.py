import sys
from pathlib import Path
from logging.config import fileConfig

from sqlalchemy import engine_from_config
from sqlalchemy import pool

from alembic import context

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# --- 开始自定义修改 ---

# 1. 将项目根目录添加到Python路径中，以便能找到项目模块
# __file__ 指向 /path/to/project/alembic/env.py
# .parent 指向 /path/to/project/alembic
# .parent.parent 指向 /path/to/project (项目根目录)
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

# 2. 从我们的项目中导入 SQLAlchemy 的声明式基类
# 现在 Python 知道去哪里找 'database' 模块了
from database.models import Base  # noqa: E402

# 3. 设置 target_metadata，这是 autogenerate 功能的核心
target_metadata = Base.metadata

# --- 结束自定义修改 ---


def run_migrations_offline():
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online():
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    # --- 新增代码：从环境变量构建数据库URL ---
    # 导入 settings 以安全地获取环境变量
    from config.settings import settings

    # 构建完整的数据库连接字符串
    DATABASE_URL = (
        f"postgresql+psycopg2://{settings.POSTGRES_USER}:{settings.POSTGRES_PASSWORD}@"
        f"{settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DB}"
    )

    # 将构建好的URL设置到Alembic的配置中
    # 这会覆盖 alembic.ini 中的 sqlalchemy.url
    config.set_main_option("sqlalchemy.url", DATABASE_URL)
    # --- 新增代码结束 ---

    # 从 alembic.ini 中读取配置并创建引擎
    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(connection=connection, target_metadata=target_metadata)

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
