# Initial Project Requirements Document

## Project Vision
基于已处理的视频素材和AI分析结果，通过Snowflake方法论实现从原始视频到结构化叙事剧本的自动化生成，并确保叙事单元编号的唯一性、故事流程的连贯性以及与前置阶段（大纲、策略）的有效集成。

## 1. Project Goals
- **Goal 1:** 确保每个叙事单元（Scene）在故事流中拥有唯一的`narrative_unit_number`，防止重复和冲突。
- **Goal 2:** 根据全局创作策略，将故事大纲中的场景进行重新编排，形成一个逻辑清晰、节奏合理的叙事流程。
- **Goal 3:** 实现Stage17与Stage14（大纲生成）和Stage16（策略规划）之间的数据无缝集成与逻辑依赖。
- **Goal 4:** 提供清晰的接口和数据结构，支持故事流的生成、存储和后续剧本创作阶段的调用。

## 2. Core Functional Points
- **Feature: 叙事单元编号唯一性保障**
  - **Description:** 在Stage17执行过程中，系统需要验证从Stage14获取的故事大纲中所有`narrative_unit_number`的唯一性，确保没有重复编号的场景。
  - **Primary User(s):** 系统内部流程
  - **Value/Benefit:** 防止因编号冲突导致的故事流编排错误和后续剧本生成失败，保证数据一致性。
- **Feature: 故事流编排引擎**
  - **Description:** 核心功能模块，负责根据Stage16提供的全局创作策略（如开篇钩子、高潮策略、结尾策略），对Stage14生成的故事大纲进行重新排序和组织，输出一个优化的叙事流程。
  - **Primary User(s):** AI剧本生成系统
  - **Value/Benefit:** 实现从松散的大纲到紧凑、吸引人的故事线的转换，提升最终视频的叙事质量。
- **Feature: 与前置阶段数据集成**
  - **Description:** Stage17需要从数据库中准确加载Stage14的`story_outline`和Stage16的`script_strategy`，并建立清晰的数据依赖关系和错误处理机制。
  - **Primary User(s):** 系统内部流程
  - **Value/Benefit:** 确保整个D2S工作流的连贯性和稳定性，使各阶段能够协同工作，形成完整的自动化 pipeline。
- **Feature: 故事流数据持久化**
  - **Description:** 将AI编排生成的故事流结果（包含重新排序的叙事单元及其编排理由）保存到数据库中，供后续Stage18（剧本生成）使用。
  - **Primary User(s):** 系统内部流程, Stage18
  - **Value/Benefit:** 为后续的剧本创作提供结构化的输入数据，并支持结果的缓存和重用，提高系统效率。

## 3. Key Considerations & Areas for Further Review
* 如何处理Stage14大纲生成中可能出现的编号逻辑错误或不一致，需要更完善的验证机制。
* 在故事流编排过程中，如果AI无法找到符合策略要求的场景，是否需要降级处理或人工干预机制。
* Stage17的输出（故事流）如何与Stage18的单场景剧本生成进行高效对接，特别是在场景被省略或重新排序的情况下。
* 当前实现中，故事流的编排理由（reasoning）主要用于调试，是否需要将其结构化以支持更高级的分析或可视化。