"""
阶段19：智能粗剪优化 (Advanced Final Cut)
基于IMO方法的多轮验证剪辑系统
"""

import json
import shutil
import statistics
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from enum import Enum
from dataclasses import dataclass

from config.settings import settings
from stages.base import BaseStage
from utils.ai_utils import advanced_ai_client
from utils.logger import get_logger
from utils.video_utils import video_processor

logger = get_logger(__name__)


class IssueType(Enum):
    """问题类型分类"""

    CRITICAL_ERROR = "critical"  # 致命错误：素材缺失、逻辑矛盾
    OPTIMIZATION_SPACE = "minor"  # 优化空间：节奏、表达优化


@dataclass
class EditingEvaluation:
    """剪辑评估结果"""

    overall_score: float
    dimension_scores: Dict[str, float]
    critical_issues: List[str]
    optimization_suggestions: List[str]
    pass_threshold: float = 4.0
    is_reasonable: bool = True


class DurationOptimizer:
    """智能时长控制优化器"""

    def __init__(self):
        self.platform_constraints = {
            "bilibili": {"optimal": (180, 300), "max": 600},  # 3-5分钟最优
            "douyin": {"optimal": (15, 60), "max": 180},  # 15秒-1分钟最优
        }

    def parse_target_duration(self, target: str) -> Tuple[float, float]:
        """解析目标时长字符串"""
        if "分钟" in target:
            # 处理 "3-5分钟" 格式
            import re

            match = re.search(r"(\d+)-(\d+)", target)
            if match:
                min_min, max_min = int(match.group(1)), int(match.group(2))
                return (min_min * 60, max_min * 60)
            # 处理 "3分钟" 格式
            match = re.search(r"(\d+)", target)
            if match:
                minutes = int(match.group(1))
                return (minutes * 60 * 0.9, minutes * 60 * 1.1)  # ±10%容差
        # 默认返回B站最优时长
        return (180, 300)

    def optimize_duration(self, edit_plan: Dict, target: str, platform: str) -> Dict:
        """智能时长优化"""
        target_range = self.parse_target_duration(target)
        current_duration = edit_plan["total_duration"]

        logger.info(f"时长优化: 当前 {current_duration:.1f}s, 目标 {target_range[0]:.1f}-{target_range[1]:.1f}s")

        if self._is_within_target(current_duration, target_range):
            logger.info("时长已达标，无需优化")
            return edit_plan

        if current_duration > target_range[1]:
            logger.info("需要压缩时长")
            return self._compress_timeline(edit_plan, target_range, platform)
        else:
            logger.info("需要延长时长")
            return self._extend_timeline(edit_plan, target_range, platform)

    def _is_within_target(self, duration: float, target_range: Tuple[float, float]) -> bool:
        """检查时长是否在目标范围内"""
        return target_range[0] <= duration <= target_range[1]

    def _compress_timeline(self, edit_plan: Dict, target_range: Tuple, platform: str) -> Dict:
        """智能压缩时长算法"""
        current_duration = edit_plan["total_duration"]
        target_duration = target_range[1]
        compression_ratio = target_duration / current_duration

        logger.info(f"应用压缩比例: {compression_ratio:.2f}")

        # 计算节拍优先级
        beat_priorities = self._calculate_beat_priorities(edit_plan["beats"])

        # 按优先级排序并选择保留的节拍
        sorted_beats = sorted(beat_priorities.items(), key=lambda x: x[1], reverse=True)
        compressed_beats = []
        accumulated_duration = 0.0

        for beat, priority in sorted_beats:
            if accumulated_duration >= target_duration:
                logger.info(f"达到目标时长 {target_duration:.1f}s，停止添加节拍")
                break

            # 对单个节拍进行压缩
            compressed_beat = self._compress_beat_intelligent(beat, compression_ratio, priority)

            # 检查是否还有空间
            if accumulated_duration + compressed_beat["duration"] <= target_duration * 1.05:  # 5%容差
                compressed_beats.append(compressed_beat)
                accumulated_duration += compressed_beat["duration"]
            else:
                # 部分裁剪以适应剩余时间
                remaining_time = target_duration - accumulated_duration
                if remaining_time > 1.0:  # 至少保留1秒
                    partially_compressed = self._partial_compress_beat(compressed_beat, remaining_time)
                    compressed_beats.append(partially_compressed)
                    accumulated_duration += partially_compressed["duration"]
                break

        edit_plan["beats"] = compressed_beats
        edit_plan["total_duration"] = accumulated_duration
        edit_plan["compression_applied"] = True

        logger.info(f"压缩完成: {len(compressed_beats)} 个节拍, 总时长 {accumulated_duration:.1f}s")
        return edit_plan

    def _calculate_beat_priorities(self, beats: List[Dict]) -> Dict[Dict, float]:
        """计算节拍优先级"""
        priorities = {}

        for beat in beats:
            priority = 1.0  # 基础优先级

            # 有旁白的节拍优先级更高
            if beat.get("audio_content"):
                priority += 0.5

            # 置信度高的节拍优先级更高
            confidence = beat.get("confidence", 0.5)
            priority += confidence * 0.3

            # 情感高点优先级更高（基于关键词检测）
            if self._is_emotional_climax(beat):
                priority += 0.8

            # 关键信息点优先级更高
            if self._contains_key_information(beat):
                priority += 0.6

            priorities[beat] = priority

        return priorities

    def _is_emotional_climax(self, beat: Dict) -> bool:
        """检测是否为情感高点"""
        emotional_keywords = ["惊人", "震惊", "激动", "感动", "高潮", "转折", "危机", "突破"]
        text = beat.get("audio_content", "") + beat.get("visual_description", "")
        return any(keyword in text for keyword in emotional_keywords)

    def _contains_key_information(self, beat: Dict) -> bool:
        """检测是否包含关键信息"""
        key_info_keywords = ["重要", "关键", "核心", "主要", "揭示", "发现", "结论", "原因"]
        text = beat.get("audio_content", "") + beat.get("visual_description", "")
        return any(keyword in text for keyword in key_info_keywords)

    def _compress_beat_intelligent(self, beat: Dict, ratio: float, priority: float) -> Dict:
        """智能压缩单个节拍"""
        # 高优先级节拍受到的压缩更少
        adjusted_ratio = ratio + (1 - ratio) * (priority - 1) * 0.3
        adjusted_ratio = max(0.3, min(1.0, adjusted_ratio))  # 限制在30%-100%之间

        compressed_beat = beat.copy()
        original_duration = beat["duration"]
        new_duration = original_duration * adjusted_ratio

        # 调整镜头时长
        if beat.get("shots"):
            total_shot_duration = sum(s["duration"] for s in beat["shots"])
            if total_shot_duration > 0:
                shot_ratio = new_duration / total_shot_duration
                compressed_shots = []
                for shot in beat["shots"]:
                    compressed_shot = shot.copy()
                    compressed_shot["duration"] = shot["duration"] * shot_ratio
                    compressed_shots.append(compressed_shot)
                compressed_beat["shots"] = compressed_shots

        compressed_beat["duration"] = new_duration
        compressed_beat["compression_ratio"] = adjusted_ratio

        return compressed_beat

    def _partial_compress_beat(self, beat: Dict, target_duration: float) -> Dict:
        """部分压缩节拍以适应剩余时间"""
        if beat["duration"] <= target_duration:
            return beat

        ratio = target_duration / beat["duration"]
        return self._compress_beat_intelligent(beat, ratio, 1.0)

    def _extend_timeline(self, edit_plan: Dict, target_range: Tuple, platform: str) -> Dict:
        """智能延长时长算法"""
        # 简单实现：复制低优先级的节拍或延长现有节拍
        current_duration = edit_plan["total_duration"]
        target_duration = target_range[0]  # 使用目标下限
        extension_needed = target_duration - current_duration

        logger.info(f"需要延长 {extension_needed:.1f}s")

        # 通过延长现有节拍来实现
        extension_per_beat = extension_needed / len(edit_plan["beats"])
        extended_beats = []

        for beat in edit_plan["beats"]:
            extended_beat = beat.copy()
            extended_beat["duration"] += extension_per_beat

            # 按比例延长镜头时长
            if beat.get("shots"):
                extended_shots = []
                for shot in beat["shots"]:
                    extended_shot = shot.copy()
                    shot_extension = extension_per_beat * (shot["duration"] / beat["duration"])
                    extended_shot["duration"] += shot_extension
                    extended_shots.append(extended_shot)
                extended_beat["shots"] = extended_shots

            extended_beats.append(extended_beat)

        edit_plan["beats"] = extended_beats
        edit_plan["total_duration"] = target_duration
        edit_plan["extension_applied"] = True

        return edit_plan


class RhythmAnalyzer:
    """节奏分析和优化器"""

    def __init__(self):
        self.rhythm_patterns = {
            "fast_paced": {"avg_shot_duration": 2.0, "variance": 0.5},
            "balanced": {"avg_shot_duration": 3.5, "variance": 1.0},
            "slow_paced": {"avg_shot_duration": 5.0, "variance": 1.5},
        }

    def analyze_and_optimize_rhythm(self, edit_plan: Dict, style: str = "balanced") -> Dict:
        """分析并优化剪辑节奏"""
        current_rhythm = self._analyze_current_rhythm(edit_plan["beats"])
        target_pattern = self.rhythm_patterns.get(style, self.rhythm_patterns["balanced"])

        logger.info(
            f"当前节奏: 平均镜头时长 {current_rhythm['avg_duration']:.2f}s, 方差 {current_rhythm['variance']:.2f}"
        )
        logger.info(f"目标节奏: 平均镜头时长 {target_pattern['avg_shot_duration']}s, 方差 {target_pattern['variance']}")

        if self._needs_rhythm_adjustment(current_rhythm, target_pattern):
            logger.info("需要进行节奏调整")
            optimized_beats = self._adjust_rhythm(edit_plan["beats"], target_pattern)
            edit_plan["beats"] = optimized_beats
            edit_plan["rhythm_optimized"] = True
        else:
            logger.info("当前节奏已符合要求")

        return edit_plan

    def _analyze_current_rhythm(self, beats: List[Dict]) -> Dict:
        """分析当前剪辑节奏"""
        shot_durations = []
        for beat in beats:
            for shot in beat.get("shots", []):
                shot_durations.append(shot["duration"])

        if not shot_durations:
            return {"avg_duration": 0, "variance": 0, "rhythm_score": 0}

        avg_duration = sum(shot_durations) / len(shot_durations)
        variance = statistics.variance(shot_durations) if len(shot_durations) > 1 else 0

        return {
            "avg_duration": avg_duration,
            "variance": variance,
            "rhythm_score": self._calculate_rhythm_score(shot_durations),
            "shot_count": len(shot_durations),
        }

    def _calculate_rhythm_score(self, durations: List[float]) -> float:
        """计算节奏得分（0-5分）"""
        if not durations:
            return 0

        # 基于时长分布的变化评估节奏质量
        avg = sum(durations) / len(durations)
        variance = sum((d - avg) ** 2 for d in durations) / len(durations)

        # 适中的变化是好的，过于单调或过于混乱都不好
        ideal_variance = avg * 0.5  # 理想方差为平均值的50%
        variance_score = 5 - abs(variance - ideal_variance) / ideal_variance * 2

        return max(0, min(5, variance_score))

    def _needs_rhythm_adjustment(self, current: Dict, target: Dict) -> bool:
        """判断是否需要节奏调整"""
        avg_diff = abs(current["avg_duration"] - target["avg_shot_duration"])
        variance_diff = abs(current["variance"] - target["variance"])

        # 如果平均时长差异超过1秒或方差差异过大，则需要调整
        return avg_diff > 1.0 or variance_diff > target["variance"] * 0.5

    def _adjust_rhythm(self, beats: List[Dict], target_pattern: Dict) -> List[Dict]:
        """调整镜头时长以匹配目标节奏"""
        adjusted_beats = []

        for beat in beats:
            adjusted_shots = []
            for shot in beat.get("shots", []):
                new_duration = self._calculate_optimal_shot_duration(
                    shot, target_pattern, beat.get("emotional_intensity", 0.5)
                )

                adjusted_shot = shot.copy()
                adjusted_shot["duration"] = new_duration
                adjusted_shot["rhythm_adjusted"] = True
                adjusted_shots.append(adjusted_shot)

            adjusted_beat = beat.copy()
            adjusted_beat["shots"] = adjusted_shots
            adjusted_beat["duration"] = sum(s["duration"] for s in adjusted_shots)
            adjusted_beats.append(adjusted_beat)

        return adjusted_beats

    def _calculate_optimal_shot_duration(self, shot: Dict, pattern: Dict, intensity: float) -> float:
        """计算最优镜头时长"""
        base_duration = pattern["avg_shot_duration"]
        variance = pattern["variance"]

        # 根据情感强度调整：高强度场景用更短的镜头
        intensity_factor = 1.0 - (intensity - 0.5) * 0.4  # 0.3 到 1.7 的范围

        # 添加随机变化以避免单调
        import random

        random_factor = 1 + random.uniform(-variance / base_duration, variance / base_duration)

        optimal_duration = base_duration * intensity_factor * random_factor
        return max(0.5, min(10.0, optimal_duration))  # 限制在0.5-10秒之间


class EditingQualityEvaluator:
    """剪辑质量评估器"""

    def evaluate_edit_quality(self, edit_plan: Dict, master_script: List[Dict]) -> EditingEvaluation:
        """多维度评估剪辑质量"""
        dimension_scores = {}

        # 1. 时长控制评估
        dimension_scores["duration_control"] = self._evaluate_duration_control(edit_plan)

        # 2. 素材匹配度评估
        dimension_scores["material_matching"] = self._evaluate_material_matching(edit_plan, master_script)

        # 3. 节奏连贯性评估
        dimension_scores["rhythm_coherence"] = self._evaluate_rhythm_coherence(edit_plan)

        # 4. 视觉连贯性评估
        dimension_scores["visual_coherence"] = self._evaluate_visual_coherence(edit_plan)

        # 5. 音画同步评估
        dimension_scores["audio_video_sync"] = self._evaluate_audio_video_sync(edit_plan)

        # 计算综合得分
        overall_score = self._calculate_weighted_score(dimension_scores)

        # 识别问题
        critical_issues = self._identify_critical_issues(dimension_scores)
        optimization_suggestions = self._generate_optimization_suggestions(dimension_scores)

        evaluation = EditingEvaluation(
            overall_score=overall_score,
            dimension_scores=dimension_scores,
            critical_issues=critical_issues,
            optimization_suggestions=optimization_suggestions,
        )

        logger.info(f"剪辑质量评估: 总分 {overall_score:.2f}/5.0")
        for dim, score in dimension_scores.items():
            logger.info(f"  {dim}: {score:.2f}/5.0")

        return evaluation

    def _evaluate_duration_control(self, edit_plan: Dict) -> float:
        """评估时长控制质量"""
        target_range = edit_plan.get("target_duration_range", (180, 300))
        current_duration = edit_plan["total_duration"]

        if target_range[0] <= current_duration <= target_range[1]:
            return 5.0  # 完美达标
        elif current_duration < target_range[0] * 0.8 or current_duration > target_range[1] * 1.2:
            return 1.0  # 严重偏离
        else:
            # 根据偏离程度线性计算
            if current_duration < target_range[0]:
                deviation = (target_range[0] - current_duration) / target_range[0]
            else:
                deviation = (current_duration - target_range[1]) / target_range[1]
            return max(1.0, 5.0 - deviation * 4.0)

    def _evaluate_material_matching(self, edit_plan: Dict, master_script: List[Dict]) -> float:
        """评估素材匹配度"""
        total_beats = len(edit_plan["beats"])
        if total_beats == 0:
            return 0.0

        matched_beats = 0
        total_confidence = 0.0

        for beat in edit_plan["beats"]:
            confidence = beat.get("confidence", 0.0)
            total_confidence += confidence

            if confidence >= 0.7:  # 70%以上置信度认为是良好匹配
                matched_beats += 1

        match_ratio = matched_beats / total_beats
        avg_confidence = total_confidence / total_beats

        # 综合匹配比例和平均置信度
        return (match_ratio * 0.6 + avg_confidence * 0.4) * 5.0

    def _evaluate_rhythm_coherence(self, edit_plan: Dict) -> float:
        """评估节奏连贯性"""
        beats = edit_plan["beats"]
        if not beats:
            return 0.0

        # 收集所有镜头时长
        shot_durations = []
        for beat in beats:
            for shot in beat.get("shots", []):
                shot_durations.append(shot["duration"])

        if len(shot_durations) < 2:
            return 3.0  # 默认中等分数

        # 计算时长变化的平滑度
        duration_changes = []
        for i in range(1, len(shot_durations)):
            change = abs(shot_durations[i] - shot_durations[i - 1])
            duration_changes.append(change)

        avg_change = sum(duration_changes) / len(duration_changes)
        avg_duration = sum(shot_durations) / len(shot_durations)

        # 变化太大或太小都不好
        change_ratio = avg_change / avg_duration if avg_duration > 0 else 0

        if 0.2 <= change_ratio <= 0.8:  # 理想的变化范围
            return 5.0
        elif change_ratio < 0.1 or change_ratio > 1.5:  # 过于单调或混乱
            return 2.0
        else:
            return 3.5

    def _evaluate_visual_coherence(self, edit_plan: Dict) -> float:
        """评估视觉连贯性"""
        beats = edit_plan["beats"]
        if not beats:
            return 0.0

        # 简化评估：检查是否有足够的视觉素材
        visual_coverage = 0
        total_duration = 0

        for beat in beats:
            beat_duration = beat.get("duration", 0)
            total_duration += beat_duration

            if beat.get("shots"):
                visual_coverage += beat_duration

        if total_duration == 0:
            return 0.0

        coverage_ratio = visual_coverage / total_duration
        return coverage_ratio * 5.0

    def _evaluate_audio_video_sync(self, edit_plan: Dict) -> float:
        """评估音画同步质量"""
        beats = edit_plan["beats"]
        if not beats:
            return 0.0

        sync_score = 0.0
        total_beats = 0

        for beat in beats:
            total_beats += 1
            audio_content = beat.get("audio_content", "")
            shots = beat.get("shots", [])

            if audio_content and shots:
                # 有音频有视频，检查时长匹配
                audio_duration = (
                    len(audio_content) / settings.NARRATION_CHAR_PER_SEC if settings.NARRATION_CHAR_PER_SEC > 0 else 3.0
                )
                visual_duration = sum(s.get("duration", 0) for s in shots)

                if visual_duration > 0:
                    duration_ratio = min(audio_duration, visual_duration) / max(audio_duration, visual_duration)
                    sync_score += duration_ratio * 5.0
                else:
                    sync_score += 2.0  # 部分分数
            elif audio_content or shots:
                # 只有音频或只有视频
                sync_score += 3.0  # 中等分数
            else:
                # 既无音频也无视频
                sync_score += 1.0  # 低分

        return sync_score / total_beats if total_beats > 0 else 0.0

    def _calculate_weighted_score(self, dimension_scores: Dict[str, float]) -> float:
        """计算加权综合得分"""
        weights = {
            "duration_control": 0.25,
            "material_matching": 0.30,
            "rhythm_coherence": 0.20,
            "visual_coherence": 0.15,
            "audio_video_sync": 0.10,
        }

        weighted_sum = 0.0
        total_weight = 0.0

        for dimension, score in dimension_scores.items():
            weight = weights.get(dimension, 0.1)
            weighted_sum += score * weight
            total_weight += weight

        return weighted_sum / total_weight if total_weight > 0 else 0.0

    def _identify_critical_issues(self, dimension_scores: Dict[str, float]) -> List[str]:
        """识别致命问题"""
        critical_issues = []

        for dimension, score in dimension_scores.items():
            if score < 2.0:  # 2分以下视为致命问题
                if dimension == "duration_control":
                    critical_issues.append("时长严重偏离目标范围")
                elif dimension == "material_matching":
                    critical_issues.append("素材匹配度过低")
                elif dimension == "rhythm_coherence":
                    critical_issues.append("剪辑节奏混乱")
                elif dimension == "visual_coherence":
                    critical_issues.append("视觉连贯性不足")
                elif dimension == "audio_video_sync":
                    critical_issues.append("音画同步问题严重")

        return critical_issues

    def _generate_optimization_suggestions(self, dimension_scores: Dict[str, float]) -> List[str]:
        """生成优化建议"""
        suggestions = []

        for dimension, score in dimension_scores.items():
            if 2.0 <= score < 4.0:  # 2-4分之间有优化空间
                if dimension == "duration_control":
                    suggestions.append("建议调整镜头时长以更好地控制总时长")
                elif dimension == "material_matching":
                    suggestions.append("建议重新筛选更匹配的素材")
                elif dimension == "rhythm_coherence":
                    suggestions.append("建议优化镜头切换节奏")
                elif dimension == "visual_coherence":
                    suggestions.append("建议增加视觉过渡和连贯性")
                elif dimension == "audio_video_sync":
                    suggestions.append("建议优化音画时长匹配")

        return suggestions


class EditingIterationManager:
    """剪辑迭代管理器"""

    def __init__(self):
        self.MAX_REFINEMENT_PASSES = 5
        self.CONSECUTIVE_PASS_REQUIREMENT = 3
        self.duration_optimizer = DurationOptimizer()
        self.rhythm_analyzer = RhythmAnalyzer()
        self.quality_evaluator = EditingQualityEvaluator()

    def process_with_imo_validation(self, master_script: List[Dict], **kwargs) -> Dict:
        """基于IMO方法的完整剪辑流程"""
        logger.info("开始IMO验证流程")

        # Step 1: 初始剪辑方案生成（严谨模式）
        initial_edit = self._generate_initial_edit_rigorously(master_script, **kwargs)

        # Step 2: 多角度自我改进
        improved_edit = self._multi_perspective_improvement(initial_edit, **kwargs)

        # Step 3-6: IMO验证循环
        return self._imo_validation_loop(improved_edit, master_script, **kwargs)

    def _generate_initial_edit_rigorously(self, script: List[Dict], **kwargs) -> Dict:
        """Step 1: 初始剪辑方案生成"""
        logger.info("Step 1: 生成初始剪辑方案")

        target_duration_str = kwargs.get("target_duration", "3-5分钟")
        target_duration = self.duration_optimizer.parse_target_duration(target_duration_str)
        platform = kwargs.get("platform", "bilibili")

        edit_plan = {
            "beats": [],
            "total_duration": 0.0,
            "target_duration_range": target_duration,
            "platform": platform,
            "confidence_scores": {},
            "material_gaps": [],
        }

        for beat_idx, beat in enumerate(script):
            beat_edit = self._process_beat_with_confidence(beat, beat_idx, edit_plan)
            edit_plan["beats"].append(beat_edit)
            edit_plan["total_duration"] += beat_edit["duration"]

        logger.info(
            f"初始剪辑方案生成完成: {len(edit_plan['beats'])} 个节拍, 总时长 {edit_plan['total_duration']:.1f}s"
        )
        return edit_plan

    def _process_beat_with_confidence(self, beat: Dict, idx: int, context: Dict) -> Dict:
        """为单个节拍生成剪辑方案并评估置信度"""
        # 模拟候选镜头获取（在实际实现中会从数据库获取）
        candidate_shots = self._get_candidate_shots_mock(beat)

        if not candidate_shots:
            logger.warning(f"节拍 {idx + 1} 缺少候选镜头")
            return {
                "beat_index": idx,
                "shots": [],
                "duration": 0.0,
                "confidence": 0.0,
                "issues": ["素材不足"],
                "audio_content": beat.get("audio_content", ""),
                "visual_description": beat.get("visual_description", ""),
            }

        # 智能镜头选择
        selected_shots = self._intelligent_shot_selection(
            candidate_shots, beat.get("audio_content", ""), beat.get("visual_description", ""), context
        )

        # 计算置信度
        confidence = self._calculate_edit_confidence(selected_shots, beat)

        return {
            "beat_index": idx,
            "shots": selected_shots,
            "duration": sum(s["duration"] for s in selected_shots),
            "confidence": confidence,
            "issues": self._identify_beat_issues(selected_shots, beat),
            "audio_content": beat.get("audio_content", ""),
            "visual_description": beat.get("visual_description", ""),
        }

    def _get_candidate_shots_mock(self, beat: Dict) -> List[Dict]:
        """模拟获取候选镜头（实际实现中应从数据库获取）"""
        # 这里返回模拟数据，实际实现中应该从数据库获取
        return [{"shot_id": i, "duration": 3.0 + i * 0.5, "confidence": 0.8} for i in range(3)]

    def _intelligent_shot_selection(self, candidates: List[Dict], audio: str, visual: str, context: Dict) -> List[Dict]:
        """智能镜头选择算法"""
        # 简化实现：选择置信度最高的镜头
        sorted_candidates = sorted(candidates, key=lambda x: x.get("confidence", 0), reverse=True)

        # 根据音频时长决定选择多少镜头
        if audio:
            audio_duration = (
                len(audio) / settings.NARRATION_CHAR_PER_SEC if settings.NARRATION_CHAR_PER_SEC > 0 else 3.0
            )
            target_duration = audio_duration
        else:
            target_duration = 4.0  # 默认4秒

        selected = []
        current_duration = 0.0

        for shot in sorted_candidates:
            if current_duration >= target_duration:
                break
            selected.append(shot)
            current_duration += shot["duration"]

        return selected

    def _calculate_edit_confidence(self, shots: List[Dict], beat: Dict) -> float:
        """计算编辑置信度"""
        if not shots:
            return 0.0

        # 基于镜头置信度的平均值
        shot_confidences = [s.get("confidence", 0.5) for s in shots]
        avg_confidence = sum(shot_confidences) / len(shot_confidences)

        # 如果有音频内容，稍微提高置信度
        if beat.get("audio_content"):
            avg_confidence = min(1.0, avg_confidence + 0.1)

        return avg_confidence

    def _identify_beat_issues(self, shots: List[Dict], beat: Dict) -> List[str]:
        """识别节拍问题"""
        issues = []

        if not shots:
            issues.append("无可用镜头")
        elif len(shots) < 2 and beat.get("audio_content"):
            issues.append("镜头数量可能不足以匹配音频内容")

        # 检查时长匹配
        if beat.get("audio_content"):
            audio_duration = (
                len(beat["audio_content"]) / settings.NARRATION_CHAR_PER_SEC
                if settings.NARRATION_CHAR_PER_SEC > 0
                else 3.0
            )
            visual_duration = sum(s["duration"] for s in shots)

            if abs(audio_duration - visual_duration) > audio_duration * 0.3:  # 差异超过30%
                issues.append("音视频时长不匹配")

        return issues

    def _multi_perspective_improvement(self, edit_plan: Dict, **kwargs) -> Dict:
        """Step 2: 多角度自我改进"""
        logger.info("Step 2: 多角度自我改进")

        # 时长优化
        target_duration_str = kwargs.get("target_duration", "3-5分钟")
        platform = kwargs.get("platform", "bilibili")

        improved_plan = self.duration_optimizer.optimize_duration(edit_plan, target_duration_str, platform)

        # 节奏优化
        # narrative_rhythm = kwargs.get("narrative_rhythm", "balanced")
        rhythm_style = "balanced"  # 可以根据narrative_rhythm映射

        improved_plan = self.rhythm_analyzer.analyze_and_optimize_rhythm(improved_plan, rhythm_style)

        logger.info("多角度改进完成")
        return improved_plan

    def _imo_validation_loop(self, edit_plan: Dict, master_script: List[Dict], **kwargs) -> Dict:
        """Step 3-6: IMO验证循环"""
        logger.info("开始IMO验证循环")

        consecutive_passes = 0
        iteration_count = 0
        evaluation = None  # 初始化evaluation变量

        while consecutive_passes < self.CONSECUTIVE_PASS_REQUIREMENT and iteration_count < 10:
            iteration_count += 1
            logger.info(f"第 {iteration_count} 轮验证")

            # Step 3: 剪辑质量验证
            evaluation = self.quality_evaluator.evaluate_edit_quality(edit_plan, master_script)

            # Step 4: 验证审查
            evaluation_review = self._review_evaluation_quality(evaluation, edit_plan)

            if evaluation_review["evaluation_is_reasonable"]:
                if evaluation.overall_score >= evaluation.pass_threshold:
                    consecutive_passes += 1
                    logger.info(f"验证通过，连续通过次数: {consecutive_passes}/{self.CONSECUTIVE_PASS_REQUIREMENT}")

                    if consecutive_passes >= self.CONSECUTIVE_PASS_REQUIREMENT:
                        logger.info("✅ 达到连续通过要求，剪辑方案最终确认")
                        edit_plan["final_evaluation"] = evaluation
                        edit_plan["validation_passed"] = True
                        return edit_plan
                else:
                    consecutive_passes = 0
                    logger.info(f"验证未通过，得分: {evaluation.overall_score:.2f}/{evaluation.pass_threshold}")

                    # Step 5: 针对性改进
                    edit_plan = self._refine_edit_based_on_evaluation(edit_plan, evaluation, master_script, **kwargs)
            else:
                logger.warning("评估被审查器认为不合理，重新评估")
                continue

        # Step 6: 最终决策
        if consecutive_passes >= self.CONSECUTIVE_PASS_REQUIREMENT:
            edit_plan["validation_passed"] = True
            return edit_plan
        else:
            logger.error("⚠️ 达到最大迭代次数仍未通过验证，标记需要人工干预")
            edit_plan["requires_manual_intervention"] = True
            edit_plan["final_evaluation"] = evaluation
            edit_plan["validation_passed"] = False
            return edit_plan

    def _review_evaluation_quality(self, evaluation: EditingEvaluation, edit_plan: Dict) -> Dict:
        """Step 4: 审查评估质量"""
        # 简化实现：检查评估是否合理
        review_result = {"evaluation_is_reasonable": True, "issues": []}

        # 检查评分是否在合理范围内
        for dimension, score in evaluation.dimension_scores.items():
            if not (0 <= score <= 5):
                review_result["evaluation_is_reasonable"] = False
                review_result["issues"].append(f"{dimension} 评分超出范围: {score}")

        # 检查致命问题与评分的一致性
        if evaluation.critical_issues and evaluation.overall_score > 3.0:
            review_result["evaluation_is_reasonable"] = False
            review_result["issues"].append("存在致命问题但总分过高")

        return review_result

    def _refine_edit_based_on_evaluation(
        self, edit_plan: Dict, evaluation: EditingEvaluation, master_script: List[Dict], **kwargs
    ) -> Dict:
        """Step 5: 基于评估进行针对性改进"""
        logger.info("Step 5: 针对性改进")

        refined_plan = edit_plan.copy()

        # 根据具体问题进行改进
        if "时长严重偏离目标范围" in evaluation.critical_issues:
            logger.info("修正时长控制问题")
            target_duration_str = kwargs.get("target_duration", "3-5分钟")
            platform = kwargs.get("platform", "bilibili")
            refined_plan = self.duration_optimizer.optimize_duration(refined_plan, target_duration_str, platform)

        if "剪辑节奏混乱" in evaluation.critical_issues:
            logger.info("修正节奏问题")
            refined_plan = self.rhythm_analyzer.analyze_and_optimize_rhythm(refined_plan, "balanced")

        if "素材匹配度过低" in evaluation.critical_issues:
            logger.info("重新筛选素材")
            # 这里可以实现重新筛选逻辑
            pass

        # 更新总时长
        refined_plan["total_duration"] = sum(beat["duration"] for beat in refined_plan["beats"])

        logger.info("针对性改进完成")
        return refined_plan


class Stage19AdvancedFinalCut(BaseStage):
    """阶段19：智能粗剪优化 (Advanced Final Cut)"""

    def __init__(self, db_manager, video_id):
        super().__init__(db_manager, video_id)
        self.editing_client = advanced_ai_client

    @property
    def stage_number(self) -> int:
        return 19

    @property
    def stage_name(self) -> str:
        return "智能粗剪优化 (Advanced Final Cut)"

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        stage17_status = self.db_manager.get_stage_status(self.video_id, 17)
        if not stage17_status or stage17_status["status"] != "completed":
            return False, "阶段17 (剧本创作) 尚未完成"
        return True, ""

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """执行智能粗剪优化"""
        try:
            # 清理旧输出（如果需要）
            if force_level == "full":
                self.logger.info("强制模式 'full': 清理所有已生成的文件")
                self._cleanup_old_outputs()

            # 1. 加载数据
            master_script = self.db_manager.get_master_script(self.video_id)
            if not master_script:
                self.logger.error("未找到剧本数据，请先运行阶段17")
                return False

            self.logger.info(f"加载了包含 {len(master_script)} 个节拍的剧本")

            # 2. 基于IMO方法的完整剪辑流程
            iteration_manager = EditingIterationManager()
            final_edit_plan = iteration_manager.process_with_imo_validation(master_script, **kwargs)

            # 3. 检查验证结果
            if not final_edit_plan.get("validation_passed", False):
                if final_edit_plan.get("requires_manual_intervention"):
                    self.logger.error("❌ 智能优化无法达到质量要求，需要人工干预")
                    self._save_diagnostic_info(final_edit_plan)
                    return False
                else:
                    self.logger.warning("⚠️ 未完全通过验证，但继续生成输出")

            # 4. 生成最终XML
            success = self._generate_final_xml(final_edit_plan)

            if success:
                self.logger.info("✅ 智能粗剪优化完成")
                # 保存评估报告
                if final_edit_plan.get("final_evaluation"):
                    self._save_evaluation_report(final_edit_plan["final_evaluation"])

            return success

        except Exception as e:
            self.logger.error(f"智能粗剪优化失败: {e}", exc_info=True)
            return False

    def _cleanup_old_outputs(self):
        """清理旧的输出文件"""
        temp_dir = settings.OUTPUT_DIR / f"temp_production_{self.video_id}"
        if temp_dir.exists():
            shutil.rmtree(temp_dir)

        # 清理XML文件
        video_info = self.get_video_info()
        if video_info and video_info.get("file_hash"):
            xml_path = settings.OUTPUT_DIR / f"{video_info['file_hash']}-{self.stage_number}-advanced_final_cut.xml"
            if xml_path.exists():
                xml_path.unlink()

    def _generate_final_xml(self, edit_plan: Dict) -> bool:
        """生成最终的FCP7 XML文件"""
        try:
            self.update_progress("生成最终XML文件...")

            # 获取视频信息
            video_info = self.get_video_info()
            video_hash = video_info.get("file_hash") if video_info else None

            if video_hash:
                xml_output_path = settings.OUTPUT_DIR / f"{video_hash}-{self.stage_number}-advanced_final_cut.xml"
            else:
                self.logger.warning("无法获取视频哈希值，使用备用命名")
                xml_output_path = (
                    settings.OUTPUT_DIR / f"video_{self.video_id}-{self.stage_number}-advanced_final_cut.xml"
                )

            project_title = (
                video_info.get("video_name", f"AutoCutter Advanced Project {self.video_id}")
                if video_info
                else f"AutoCutter Advanced Project {self.video_id}"
            )

            # 转换编辑计划为时间线事件
            video_events, audio_events = self._convert_edit_plan_to_timeline(edit_plan)

            if not video_events and not audio_events:
                self.logger.error("无法生成有效的时间线事件")
                return False

            # 生成XML
            success = video_processor.generate_fcp7_xml_from_timeline(
                video_events=video_events,
                audio_events=audio_events,
                output_path=xml_output_path,
                title=project_title,
            )

            if success:
                self.logger.info(f"✅ XML文件生成成功: {xml_output_path}")
                return True
            else:
                self.logger.error("❌ XML文件生成失败")
                return False

        except Exception as e:
            self.logger.error(f"生成XML文件时出错: {e}", exc_info=True)
            return False

    def _convert_edit_plan_to_timeline(self, edit_plan: Dict) -> Tuple[List[Dict], List[Dict]]:
        """将编辑计划转换为时间线事件"""
        video_events = []
        audio_events = []
        timeline_pos = 0.0

        for beat in edit_plan["beats"]:
            beat_duration = beat.get("duration", 0)

            # 处理视频镜头
            for shot in beat.get("shots", []):
                shot_duration = shot.get("duration", 0)

                # 创建视频事件（模拟，实际需要真实的文件路径）
                video_event = {
                    "path": Path(f"mock_shot_{shot.get('shot_id', 0)}.mp4"),  # 实际应从数据库获取
                    "timeline_start": timeline_pos,
                    "timeline_end": timeline_pos + shot_duration,
                    "timeline_duration": shot_duration,
                    "source_in": 0,
                    "source_out": shot_duration,
                    "track_index": 0,
                }
                video_events.append(video_event)
                timeline_pos += shot_duration

            # 处理音频内容
            audio_content = beat.get("audio_content", "")
            if audio_content:
                # 这里需要实际生成TTS音频文件
                audio_duration = (
                    len(audio_content) / settings.NARRATION_CHAR_PER_SEC if settings.NARRATION_CHAR_PER_SEC > 0 else 3.0
                )

                audio_event = {
                    "path": Path(f"mock_tts_{beat.get('beat_index', 0)}.mp3"),  # 实际应生成TTS
                    "timeline_start": timeline_pos - beat_duration,  # 与视频同步
                    "timeline_end": timeline_pos - beat_duration + audio_duration,
                    "timeline_duration": audio_duration,
                    "source_in": 0,
                    "source_out": audio_duration,
                }
                audio_events.append(audio_event)

        return video_events, audio_events

    def _save_diagnostic_info(self, edit_plan: Dict):
        """保存诊断信息供人工分析"""
        try:
            diagnostic_path = settings.OUTPUT_DIR / f"diagnostic_{self.video_id}_stage_{self.stage_number}.json"

            diagnostic_data = {
                "video_id": self.video_id,
                "stage_number": self.stage_number,
                "validation_passed": edit_plan.get("validation_passed", False),
                "requires_manual_intervention": edit_plan.get("requires_manual_intervention", False),
                "total_duration": edit_plan.get("total_duration", 0),
                "target_duration_range": edit_plan.get("target_duration_range", []),
                "beats_count": len(edit_plan.get("beats", [])),
                "final_evaluation": edit_plan.get("final_evaluation").__dict__
                if edit_plan.get("final_evaluation")
                else None,
                "material_gaps": edit_plan.get("material_gaps", []),
            }

            with open(diagnostic_path, "w", encoding="utf-8") as f:
                json.dump(diagnostic_data, f, ensure_ascii=False, indent=2, default=str)

            self.logger.info(f"诊断信息已保存: {diagnostic_path}")

        except Exception as e:
            self.logger.error(f"保存诊断信息失败: {e}")

    def _save_evaluation_report(self, evaluation: EditingEvaluation):
        """保存评估报告"""
        try:
            self.db_manager.save_stage_output(
                video_id=self.video_id,
                stage_number=self.stage_number,
                stage_name=self.stage_name,
                output_type="advanced_editing_evaluation",
                output_data={
                    "overall_score": evaluation.overall_score,
                    "dimension_scores": evaluation.dimension_scores,
                    "critical_issues": evaluation.critical_issues,
                    "optimization_suggestions": evaluation.optimization_suggestions,
                    "pass_threshold": evaluation.pass_threshold,
                },
            )
            self.logger.info("✅ 评估报告已保存到数据库")

        except Exception as e:
            self.logger.error(f"保存评估报告失败: {e}")
