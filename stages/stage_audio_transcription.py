"""
阶段2：音频转写与分析
使用Funasr对整个视频的音轨进行VAD和带时间戳的语音转文字。
"""

import shutil
from pathlib import Path
from typing import Optional

import demucs.separate
import torch  # 新增此行

from config.settings import settings
from stages.base import BaseStage
from utils.subtitle_parser import parse_subtitle_file
from utils.transcription_utils import get_transcriber
from utils.video_utils import video_processor


class Stage2AudioTranscription(BaseStage):
    """阶段2：音频转写与分析"""

    @property
    def stage_number(self) -> int:
        return 2

    @property
    def stage_name(self) -> str:
        return "音频转写与分析"

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        stage1_status = self.db_manager.get_stage_status(self.video_id, 1)
        if not stage1_status or stage1_status["status"] != "completed":
            return False, "阶段1（微观场景分析）尚未完成"
        return True, ""

    def _save_and_associate_transcripts(self, transcripts: list):
        """保存转录/字幕结果并关联到镜头"""
        if not transcripts:
            self.logger.warning("转录/解析未返回任何结果。")
            # 即使没有结果，也视为成功，因为视频可能无语音
        else:
            self.update_progress(f"保存 {len(transcripts)} 条转录/字幕结果")
            self.db_manager.save_transcripts(self.video_id, transcripts)

        self.update_progress("将对话文本关联到视频镜头...")
        self.db_manager.associate_dialogue_to_shots(self.video_id)

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """执行音频转写"""
        try:
            # force_level: 'soft' 仅清理数据库中的转录结果，保留音频文件；'full' 清理所有音频文件和数据库记录。
            video_info = self.get_video_info()
            if not video_info or not video_info.get("input_file_path") or not video_info.get("file_hash"):
                self.logger.error("视频信息不完整（缺少文件路径或哈希值），无法提取音频。")
                return False

            input_video_path = Path(video_info["input_file_path"])
            file_hash = video_info["file_hash"]
            # 音频目录现在使用视频哈希作为子目录，以更好地组织文件
            audio_dir = settings.OUTPUT_DIR / "audio" / file_hash
            full_audio_path = audio_dir / "full_audio.mp3"

            subtitles_path = kwargs.get("subtitles")

            if force_level:
                self.logger.info(f"强制模式 '{force_level}': 正在清理旧的数据...")
                self.db_manager.clear_transcription_data(self.video_id)
                if force_level == "full":
                    self.logger.info("强制模式 'full': 正在清理所有缓存的音频文件...")
                    if audio_dir.exists():
                        shutil.rmtree(audio_dir)

            # 在任何可能的清理操作后，确保音频目录存在
            audio_dir.mkdir(parents=True, exist_ok=True)

            if subtitles_path:
                # 优先路径：从字幕文件加载
                self.logger.info(f"检测到字幕文件参数，将从 {subtitles_path} 加载字幕，跳过自动转写。")
                self.update_progress(f"解析字幕文件: {subtitles_path.name}")
                try:
                    skip_until = kwargs.get("skip_until", 0.0)
                    transcripts = parse_subtitle_file(Path(subtitles_path), skip_until_seconds=skip_until)
                    self._save_and_associate_transcripts(transcripts)
                    return True  # 处理完成，直接返回
                except FileNotFoundError:
                    self.logger.error(f"指定的字幕文件不存在: {subtitles_path}")
                    return False
                except Exception as e:
                    self.logger.error(f"解析字幕文件失败: {e}")
                    return False

            # 如果代码执行到这里，说明没有提供字幕文件，执行完整的自动转写流程
            self.logger.info("未提供字幕文件，将执行自动音频转写流程。")

            # 1. 提取完整音轨
            self.update_progress("提取完整音轨")
            # 如果是 full 模式，文件已被删除，这里会重新提取
            if not full_audio_path.exists():
                if not video_processor.extract_full_audio(
                    input_video_path, full_audio_path, settings.AUDIO_BITRATE
                ):
                    self.logger.error("提取完整音轨失败。")
                    return False
            else:
                self.logger.info(f"找到缓存的完整音轨文件: {full_audio_path}")

            # 2. 使用 Demucs 分离人声
            self.update_progress("分离人声")
            vocals_audio_path = audio_dir / "vocals.mp3"

            # 如果是 full 模式，文件已被删除，这里会重新分离
            if not vocals_audio_path.exists():
                self.logger.info("未找到缓存的人声文件，开始使用 Demucs 分离人声...")

                demucs_model_output_dir = audio_dir / "htdemucs"
                if demucs_model_output_dir.exists(): # 强制模式下，之前可能已删除，这里再次确认
                    shutil.rmtree(demucs_model_output_dir)

                # --- 动态设备检测 ---
                if torch.cuda.is_available():
                    device = "cuda"
                elif torch.backends.mps.is_available():
                    device = "mps"
                else:
                    device = "cpu"
                self.logger.info(f"Demucs 将使用设备: {device}")

                demucs_args = [
                    "--mp3",
                    "--mp3-bitrate",
                    settings.AUDIO_BITRATE.rstrip("k"),
                    "--device",
                    device,
                    "--out",
                    str(audio_dir),
                    str(full_audio_path),
                ]
                demucs.separate.main(demucs_args)

                # 使用 glob 查找生成的 vocals.mp3 文件，避免硬编码模型名称
                search_pattern = f"*/{full_audio_path.stem}/vocals.mp3"
                found_files = list(audio_dir.glob(search_pattern))

                if not found_files:
                    self.logger.error(f"Demucs 执行完毕，但未在 {audio_dir} 中找到预期的 vocals.mp3 文件。")
                    return False

                generated_vocals_file = found_files[0]
                # 获取模型输出的根目录 (e.g., .../audio/hash/htdemucs)
                model_output_dir = generated_vocals_file.parent.parent

                try:
                    shutil.move(str(generated_vocals_file), vocals_audio_path)
                    self.logger.info(f"人声文件已保存到: {vocals_audio_path}")
                finally:
                    # 无论移动是否成功，都清理整个模型输出目录
                    if model_output_dir.exists():
                        shutil.rmtree(model_output_dir)
            else:
                self.logger.info(f"找到缓存的人声文件: {vocals_audio_path}")

            # 3. 对分离出的人声执行转写
            self.update_progress("开始自动语音转写")
            transcriber = get_transcriber()
            transcripts = transcriber.transcribe(vocals_audio_path)

            # 保存转写结果并关联镜头
            self._save_and_associate_transcripts(transcripts)

            return True
        except Exception as e:
            self.logger.error(f"执行阶段 {self.stage_number} ({self.stage_name}) 时出错: {e}", exc_info=True)
            return False
