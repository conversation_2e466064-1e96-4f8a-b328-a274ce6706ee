"""
阶段13：D2S 读者模块 (Reader)
第三步：图谱精炼
"""

from typing import Optional

from stages.base import BaseStage
from utils.graph_utils import GraphProcessor


class Stage13ReaderRefine(BaseStage):
    """阶段13：D2S 读者模块 - 图谱精炼"""

    def __init__(self, db_manager, video_id):
        super().__init__(db_manager, video_id)

    @property
    def stage_number(self) -> int:
        return 13

    @property
    def stage_name(self) -> str:
        return "D2S Reader (图谱精炼)"

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        stage12_status = self.db_manager.get_stage_status(self.video_id, 12)
        if not stage12_status or stage12_status["status"] != "completed":
            return False, "阶段12 (D2S Reader 因果推断) 尚未完成"

        causal_graph = self.db_manager.get_stage_output(self.video_id, 12, "causal_graph")
        if not causal_graph:
            return False, "数据库中未找到因果图谱数据"

        if not self.db_manager.get_all_research_summaries(self.video_id):
            return False, "数据库中未找到研究资料摘要"

        return True, ""

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """执行图谱精炼，主要是破除逻辑循环"""
        try:
            if force_level:
                self.logger.info(f"强制模式 ({force_level}): 清理旧的精炼后图谱数据。")
                self.db_manager.clear_stage_output(self.video_id, self.stage_number, "refined_causal_graph")

            # 检查数据库中是否已有输出
            existing_output = self.db_manager.get_stage_output(self.video_id, self.stage_number, "refined_causal_graph")
            if existing_output:
                self.logger.info("从数据库加载已缓存的精炼后图谱数据。")
                return True

            # 1. 从数据库加载因果图谱
            causal_graph = self.db_manager.get_stage_output(self.video_id, 12, "causal_graph")
            if not causal_graph:
                self.logger.error("无法从数据库加载前置阶段的因果图谱数据。")
                return False

            nodes = causal_graph.get("nodes", [])
            edges = causal_graph.get("edges", [])
            if not nodes or not edges:
                self.logger.warning("输入的因果图谱为空或不完整，直接保存为空图谱。")
                self.db_manager.save_stage_output(
                    video_id=self.video_id,
                    stage_number=self.stage_number,
                    stage_name=self.stage_name,
                    output_type="refined_causal_graph",
                    output_data=causal_graph,
                )
                return True

            # 2. 初始化图谱处理器并执行破环
            self.update_progress("正在检查并精炼图谱逻辑...")
            graph_processor = GraphProcessor(nodes, edges)
            removed_edges, refined_edges = graph_processor.break_cycles_greedy()

            # 3. 保存精炼后的图谱到数据库
            refined_graph = {"nodes": nodes, "edges": refined_edges}
            self.db_manager.save_stage_output(
                video_id=self.video_id,
                stage_number=self.stage_number,
                stage_name=self.stage_name,
                output_type="refined_causal_graph",
                output_data=refined_graph,
            )

            self.logger.info(f"✅ 图谱精炼完成。移除了 {len(removed_edges)} 条边以破除循环，并保存到数据库。")
            return True
        except Exception as e:
            self.logger.error(f"执行阶段 {self.stage_number} ({self.stage_name}) 时出错: {e}", exc_info=True)
            return False
