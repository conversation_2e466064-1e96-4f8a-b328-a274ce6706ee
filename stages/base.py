"""
基础阶段类
定义所有处理阶段的通用接口和功能
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional

from database.models import DatabaseManager
from utils.logger import get_logger


class BaseStage(ABC):
    """基础阶段类"""

    def __init__(self, db_manager: DatabaseManager, video_id: int):
        self.db_manager = db_manager
        self.video_id = video_id
        self.logger = get_logger(self.__class__.__name__)
        self.force_level: Optional[str] = None

    @property
    @abstractmethod
    def stage_number(self) -> int:
        """阶段编号"""
        pass

    @property
    @abstractmethod
    def stage_name(self) -> str:
        """阶段名称"""
        pass

    @abstractmethod
    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件，返回(是否满足, 错误信息)"""
        pass

    @abstractmethod
    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """执行阶段处理，返回是否成功"""
        pass

    def can_run(self) -> tuple[bool, str]:
        """检查是否可以运行此阶段"""
        # 检查当前阶段状态
        status = self.db_manager.get_stage_status(self.video_id, self.stage_number)
        if not status:
            return False, f"找不到阶段 {self.stage_number} 的状态记录"

        if status["status"] == "completed":
            return False, f"阶段 {self.stage_number} 已经完成"

        # 检查前置条件
        can_proceed, error_msg = self.check_prerequisites()
        if not can_proceed:
            return False, f"前置条件不满足: {error_msg}"

        return True, ""

    def run(self, **kwargs) -> bool:
        """运行此阶段"""
        force_level = kwargs.pop("force_level", None)

        # 步骤 1: 检查是否可以运行
        # 强制模式下，直接跳过此检查
        if not force_level:
            status = self.db_manager.get_stage_status(self.video_id, self.stage_number)
            if status and status.get("status") == "completed":
                self.logger.info(f"阶段 {self.stage_number} ({self.stage_name}) 已完成，跳过。")
                return True

        # 步骤 2: 检查前置条件（所有模式下都必须检查）
        can_proceed, error_msg = self.check_prerequisites()
        if not can_proceed:
            self.logger.error(f"无法执行阶段 {self.stage_number}，前置条件不满足: {error_msg}")
            # 将此失败状态记录到数据库
            self.db_manager.update_stage_status(
                self.video_id, self.stage_number, "failed", error_message=f"前置条件不满足: {error_msg}"
            )
            return False

        # 如果是强制模式，打印日志
        if force_level:
            self.logger.info(f"--- 强制执行模式 (级别: {force_level}) ---")

        # 步骤 3: 更新状态为处理中并执行
        self.update_progress(f"开始执行 {self.stage_name}")
        try:
            success = self.execute(force_level=force_level, **kwargs)
            if success:
                self.db_manager.update_stage_status(
                    self.video_id, self.stage_number, "completed", progress_info=f"{self.stage_name} 完成"
                )
                return True
            else:
                self.db_manager.update_stage_status(
                    self.video_id, self.stage_number, "failed", error_message=f"{self.stage_name} 执行失败"
                )
                return False

        except Exception as e:
            self.logger.error(f"执行阶段 {self.stage_number} ({self.stage_name}) 时发生未捕获的异常: {e}", exc_info=True)
            self.db_manager.update_stage_status(self.video_id, self.stage_number, "failed", error_message=str(e))
            return False

    def update_progress(self, progress_info: str):
        """更新进度信息"""
        self.db_manager.update_stage_status(self.video_id, self.stage_number, "processing", progress_info=progress_info)
        self.logger.info(f"进度更新: {progress_info}")

    def get_video_info(self) -> Optional[Dict[str, Any]]:
        """获取视频信息"""
        return self.db_manager.get_video_by_id(self.video_id)
