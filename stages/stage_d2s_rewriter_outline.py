"""
阶段14：D2S 重写者模块 (Rewriter)
第一步：大纲生成
"""

import json
from typing import Optional

from config.schemas import StoryOutlineResponse
from config.settings import settings
from stages.base import BaseStage
from utils.ai_utils import advanced_ai_client


class Stage14RewriterOutline(BaseStage):
    """阶段14：D2S 重写者模块 - 大纲生成"""

    def __init__(self, db_manager, video_id):
        super().__init__(db_manager, video_id)
        self.rewriter_client = advanced_ai_client

    @property
    def stage_number(self) -> int:
        return 14

    @property
    def stage_name(self) -> str:
        return "D2S Rewriter (大纲生成)"

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        stage13_status = self.db_manager.get_stage_status(self.video_id, 13)
        if not stage13_status or stage13_status["status"] != "completed":
            return False, "阶段13 (D2S Reader 图谱精炼) 尚未完成"

        creative_brief = self.db_manager.get_stage_output(self.video_id, 10, "creative_brief")
        if not creative_brief:
            return False, "数据库中未找到主上下文数据 (来自阶段10)"

        refined_graph = self.db_manager.get_stage_output(self.video_id, 13, "refined_causal_graph")
        if not refined_graph:
            return False, "数据库中未找到精炼后的因果图谱数据 (来自阶段13)"

        return True, ""

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """执行从因果图谱到故事大纲的转换"""
        if force_level:
            self.logger.info(f"强制模式 ({force_level}): 清理旧的故事大纲数据。")
            self.db_manager.clear_stage_output(self.video_id, self.stage_number, "story_outline")

        # 检查数据库中是否已有输出
        existing_output = self.db_manager.get_stage_output(self.video_id, self.stage_number, "story_outline")
        if existing_output:
            self.logger.info("从数据库加载已缓存的故事大纲数据。")
            return True

        # 1. 从数据库加载所需数据
        try:
            creative_brief = self.db_manager.get_stage_output(self.video_id, 10, "creative_brief")
            refined_graph = self.db_manager.get_stage_output(self.video_id, 13, "refined_causal_graph")
            research_summary = self.db_manager.get_all_research_summaries(self.video_id)
            tagline_data = self.db_manager.get_stage_output(self.video_id, 4, "tagline") or {}
            tagline = tagline_data.get("tagline", "无")

            if not creative_brief or not refined_graph:
                self.logger.error("无法从数据库加载前置阶段的输出数据。")
                return False

        except Exception as e:
            self.logger.error(f"无法加载前置数据: {e}")
            return False

        # --- 【核心修改】预处理因果图谱，将 source_scene_numbers 字段重命名为 source_scene_ids ---
        if refined_graph and refined_graph.get("nodes"):
            for node in refined_graph["nodes"]:
                if "source_scene_numbers" in node:
                    # AI大纲生成提示词需要 'source_scene_ids' 字段，所以在此重命名。
                    # 数据已是整数列表，无需转换。
                    node["source_scene_ids"] = node["source_scene_numbers"]
                    del node["source_scene_numbers"]
                # 为安全起见，清理可能残留的旧字段
                if "source_clip_ids" in node:
                    del node["source_clip_ids"]
        # --- 核心修改结束 ---

        # 2. 调用AI进行大纲生成
        self.update_progress("AI正在将因果图谱转换为故事大纲...")
        try:
            from config.prompts import OUTLINE_GENERATION_PROMPT

            prompt = OUTLINE_GENERATION_PROMPT.format(
                tagline=tagline,
                research_summary=research_summary,
                causal_graph_json=json.dumps(refined_graph, ensure_ascii=False, indent=2),
                project_info_json=json.dumps(creative_brief.get("project_info"), ensure_ascii=False, indent=2),
                characters_json=json.dumps(creative_brief.get("characters"), ensure_ascii=False, indent=2),
                language=settings.SCRIPT_LANGUAGE,
            )

            result = self.rewriter_client.call_ai_with_tool(prompt, response_model=StoryOutlineResponse)

            if not result or not result.story_outline:
                self.logger.error("AI未能生成任何有效的故事大纲。")
                return False

            # 3. 保存到数据库
            self.db_manager.save_stage_output(
                video_id=self.video_id,
                stage_number=self.stage_number,
                stage_name=self.stage_name,
                output_type="story_outline",
                output_data=result.model_dump(),
            )

            self.logger.info(f"✅ 成功生成包含 {len(result.story_outline)} 个场景的故事大纲并保存到数据库。")
            return True

        except Exception as e:
            self.logger.error(f"执行阶段 {self.stage_number} ({self.stage_name}) 时出错: {e}", exc_info=True)
            return False
