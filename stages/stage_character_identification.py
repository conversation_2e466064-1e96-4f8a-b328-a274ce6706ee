"""
阶段5：角色识别与聚类
对阶段1提取的所有人脸进行聚类，识别出视频中的主要角色。
"""

from typing import Optional, Sequence

import numpy as np
from sklearn.cluster import DBSCAN
from sqlalchemy import func

from config.settings import settings
from database.models import Characters, FaceEmbeddings, Shots
from stages.base import BaseStage


class Stage5CharacterIdentification(BaseStage):
    """阶段5：角色识别与聚类"""

    @property
    def stage_number(self) -> int:
        return 5  # ← 原 4 +1

    @property
    def stage_name(self) -> str:
        return "角色识别与聚类"

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        # 依赖关系修正：角色识别仅依赖于阶段1（镜头分析）提取了人脸
        stage1_status = self.db_manager.get_stage_status(self.video_id, 1)
        if not stage1_status or stage1_status["status"] != "completed":
            return False, "阶段1（镜头分析）尚未完成"
        return True, ""

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """执行人脸识别与聚类"""
        try:
            if force_level:
                self.logger.info(f"强制模式 '{force_level}': 正在清理旧的角色和人脸聚类数据...")
                self._clear_character_data()
                self.logger.info("旧数据清理完成。")

            # 1. 获取所有未分配的人脸
            self.update_progress("获取未分配角色的人脸...")
            unassigned_faces = self._get_unassigned_faces()
            if not unassigned_faces:
                self.logger.info("没有需要识别或聚类的新人脸。")
                # 即使没有新人脸，如果之前有角色，也需要重新更新一次镜头的角色标注
                self.logger.info("重新计算并更新镜头中的角色标注...")
                self._update_shots_with_characters()
                self.logger.info("角色标注更新完成。")
                return True

            # 2. 无监督聚类：对所有人脸进行聚类
            self.update_progress(f"开始对 {len(unassigned_faces)} 个人脸进行无监督聚类...")
            self._cluster_faces(unassigned_faces)
            self.logger.info("人脸聚类完成。")

            # 3. 更新所有镜头的角色标注
            self.update_progress("更新镜头中的角色标注...")
            self._update_shots_with_characters()
            self.logger.info("角色标注更新完成。")

            return True
        except Exception as e:
            self.logger.error(f"执行阶段 {self.stage_number} ({self.stage_name}) 时出错: {e}", exc_info=True)
            return False

    def _clear_character_data(self):
        """清理此视频之前的所有角色和人脸聚类结果"""
        with self.db_manager.get_session() as session:
            # 1. 删除旧的角色记录
            session.query(Characters).filter_by(video_id=self.video_id).delete(synchronize_session=False)

            # 2. 正确地清空 face_embeddings 表中的 character_id
            # 创建一个子查询，找到所有属于该视频的镜头ID
            subquery = session.query(Shots.id).filter(Shots.video_id == self.video_id).scalar_subquery()
            # 更新所有关联到这些镜头的人脸记录
            session.query(FaceEmbeddings).filter(FaceEmbeddings.shot_id.in_(subquery)).update(
                {"character_id": None}, synchronize_session=False
            )

            # 3. 清理镜头中的角色标注
            session.query(Shots).filter_by(video_id=self.video_id).update(
                {"characters_present": None}, synchronize_session=False
            )

            self.logger.info("已清理旧的角色数据。")

    def _get_unassigned_faces(self) -> Sequence[tuple[int, bytes]]:
        """获取所有未分配角色的人脸的ID和embedding"""
        with self.db_manager.get_session() as session:
            # 只查询我们需要的原始数据，而不是整个ORM对象
            rows = (
                session.query(FaceEmbeddings.id, FaceEmbeddings.embedding)
                .join(Shots)
                .filter(Shots.video_id == self.video_id, FaceEmbeddings.character_id.is_(None))
                .all()
            )
            # 将SQLAlchemy Row对象转换为普通tuple
            return [(row[0], row[1]) for row in rows]

    def _cluster_faces(self, faces_data: Sequence[tuple[int, bytes]]):
        """对人脸进行DBSCAN聚类"""
        if not faces_data:
            return

        # 解包 face_ids 和 embedding_bytes
        face_ids = [item[0] for item in faces_data]
        embedding_bytes_list = [item[1] for item in faces_data]

        embeddings = np.array([np.frombuffer(b, dtype=np.float32) for b in embedding_bytes_list])
        db = DBSCAN(eps=settings.DBSCAN_EPS, min_samples=settings.DBSCAN_MIN_SAMPLES, metric="cosine", n_jobs=-1).fit(
            embeddings
        )
        labels = db.labels_

        with self.db_manager.get_session() as session:
            cluster_map = {}
            for label in set(labels):
                if label == -1:
                    continue

                cluster_indices = np.where(labels == label)[0]
                cluster_embeddings = embeddings[cluster_indices]
                representative_embedding = np.mean(cluster_embeddings, axis=0)

                new_char = Characters(
                    video_id=self.video_id,
                    name=f"未知角色 {label + 1}",
                    source="clustered",
                    face_count=len(cluster_indices),
                    representative_embedding=representative_embedding.tobytes(),
                )
                session.add(new_char)
                session.flush()
                cluster_map[label] = new_char.id

            updates = []
            for face_id, label in zip(face_ids, labels):
                if label != -1:
                    updates.append({"id": face_id, "character_id": cluster_map[label]})

            if updates:
                session.bulk_update_mappings(FaceEmbeddings.__mapper__, updates)
                self.logger.info(f"已将 {len(updates)} 个人脸特征批量更新到 {len(cluster_map)} 个角色中。")

    def _update_shots_with_characters(self):
        """在所有镜头中聚合和标注出现的角色"""
        with self.db_manager.get_session() as session:
            query = (
                session.query(Shots.id, func.array_agg(func.distinct(FaceEmbeddings.character_id)))
                .join(FaceEmbeddings, Shots.id == FaceEmbeddings.shot_id)
                .filter(Shots.video_id == self.video_id, FaceEmbeddings.character_id.isnot(None))
                .group_by(Shots.id)
            )

            updates = []
            for shot_id, char_ids in query.all():
                updates.append({"id": shot_id, "characters_present": char_ids})

            if updates:
                session.bulk_update_mappings(Shots.__mapper__, updates)
                self.logger.info(f"已更新 {len(updates)} 个镜头的角色标注信息。")
            else:
                self.logger.info("没有需要更新角色标注的镜头。")
