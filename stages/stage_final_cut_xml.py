"""
阶段21：成片合成 (Final Cut)
最后一步，根据剧本先粗剪，再精剪，生成成片
"""

import json
import shutil
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

from config.prompts import ROUGH_CUT_REFINEMENT_PROMPT
from config.schemas import RoughCutRefinementResponse
from config.settings import settings
from stages.base import BaseStage
from utils.ai_utils import advanced_ai_client  # <-- 修改此行
from utils.retry_utils import retry_on_exception
from utils.tts_utils import get_tts_client
from utils.video_utils import video_processor


class Stage21FinalCutXML(BaseStage):  # <-- 修改类名和编号
    """阶段21：成片合成 (Final Cut XML)"""  # <-- 修改注释和编号

    def __init__(self, db_manager, video_id):
        super().__init__(db_manager, video_id)
        self.editing_client = advanced_ai_client

    @property
    def stage_number(self) -> int:
        return 21  # <-- 修改阶段号

    @property
    def stage_name(self) -> str:
        return "成片合成 (Final Cut XML)"  # <-- 修改阶段名

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        stage20_status = self.db_manager.get_stage_status(self.video_id, 20)  # <-- 修改阶段号
        if not stage20_status or stage20_status["status"] != "completed":
            return False, "阶段20 (时长控制) 尚未完成"  # <-- 修改阶段名和编号
        return True, ""

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """执行自动化生产与合成"""
        try:
            temp_dir = settings.OUTPUT_DIR / f"temp_production_{self.video_id}"
            if force_level == "full":
                self.logger.info("强制模式 'full': 清理所有已生成的TTS音频、剪辑计划和XML文件。")
                self.db_manager.clear_stage_output(self.video_id, self.stage_number, "final_cut_plan")
                if temp_dir.exists():
                    shutil.rmtree(temp_dir)
                # 旧规则文件
                # 新规则文件：<video_hash>/21-final_cut.xml
                video_info = self.get_video_info()
                if video_info and video_info.get("file_hash"):
                    video_output_dir = settings.OUTPUT_DIR / video_info["file_hash"]
                    xml_path = video_output_dir / f"{self.stage_number}-final_cut.xml"
                    if xml_path.exists():
                        xml_path.unlink()

            # --- 【核心修改】加载经过时长控制的剧本 ---
            controlled_script_data = self.db_manager.get_stage_output(self.video_id, 20, "duration_controlled_script")
            if not controlled_script_data or "refined_script_beats" not in controlled_script_data:
                self.logger.error("未找到经过时长控制的剧本，请先运行阶段20。")
                return False
            master_script = controlled_script_data["refined_script_beats"]
            # --- 修改结束 ---

            return self._produce_from_script_by_beats(master_script, **kwargs)

        except Exception as e:
            self.logger.error(f"执行阶段 {self.stage_number} ({self.stage_name}) 时出错: {e}", exc_info=True)
            return False

    def _optimize_timeline_duration(
        self, beat_results: List[Dict[str, Any]], target_duration_range: Tuple[float, float]
    ) -> List[Dict[str, Any]]:
        """根据目标时长，通过移除低优先级节拍来压缩时间线。"""
        # 预先计算每个节拍的视觉时长和总段落时长
        for r in beat_results:
            r["visual_duration"] = sum(s.get("duration_sec", s["end_time"] - s["start_time"]) for s in r["final_shots"])
            r["segment_duration"] = max(r["audio_duration"], r["visual_duration"])

        current_duration = sum(r["segment_duration"] for r in beat_results)
        target_max_duration = target_duration_range[1]

        self.logger.info(f"开始时长优化。当前总时长: {current_duration:.2f}s, 目标上限: {target_max_duration:.2f}s")

        if current_duration <= target_max_duration:
            self.logger.info("当前时长已在目标范围内，无需压缩。")
            return beat_results

        # --- 压缩逻辑 ---
        # 1. 计算节拍优先级
        beats_with_priority = []
        for i, beat in enumerate(beat_results):
            # 简单优先级：有旁白 > 纯视觉。值越小优先级越低。
            priority = 1.0
            if beat.get("audio_path"):  # 有旁白
                priority = 2.0

            # 纯视觉节拍且时长短的，优先级最低
            if not beat.get("audio_path") and beat["segment_duration"] < 2.0:
                priority = 0.5

            beats_with_priority.append(
                {"original_index": i, "priority": priority, "duration": beat["segment_duration"]}
            )

        # 2. 按优先级升序排序，准备移除
        beats_to_remove_candidates = sorted(beats_with_priority, key=lambda x: x["priority"])

        removed_indices = set()
        duration_to_cut = current_duration - target_max_duration
        cut_duration = 0.0

        self.logger.info(f"需要压缩时长: {duration_to_cut:.2f}s")

        for candidate in beats_to_remove_candidates:
            if cut_duration >= duration_to_cut:
                break

            removed_indices.add(candidate["original_index"])
            cut_duration += candidate["duration"]
            self.logger.info(
                f"计划移除节拍 (索引: {candidate['original_index']}, 优先级: {candidate['priority']:.2f}, "
                f"时长: {candidate['duration']:.2f}s) 以压缩时间线。"
            )

        # 3. 构建新的 beat_results 列表
        optimized_beat_results = [beat for i, beat in enumerate(beat_results) if i not in removed_indices]

        final_duration = sum(r["segment_duration"] for r in optimized_beat_results)
        self.logger.info(f"时长优化完成。移除了 {len(removed_indices)} 个节拍。最终时长: {final_duration:.2f}s")

        return optimized_beat_results

    def _produce_from_script_by_beats(self, master_script: List[Dict[str, Any]], **kwargs) -> bool:
        """根据结构化的纯视觉剧本，组装时间线。"""
        self.update_progress(f"开始组装 {len(master_script)} 个叙事单元的视觉序列...")

        video_events: List[Dict[str, Any]] = []
        timeline_pos, current_video_track_index = 0.0, 0

        # 1. 遍历每个叙事单元（ScriptBeat 容器）
        for beat_container in master_script:
            source_unit_num = beat_container.get("source_narrative_unit_number")
            visual_beats = beat_container.get("visual_beats", [])

            if not visual_beats:
                self.logger.warning(f"叙事单元 {source_unit_num} 不包含任何视觉节拍，已跳过。")
                continue

            # 2. 遍历单元内的每个视觉节拍（VisualBeat）
            for visual_beat in visual_beats:
                selected_ids = visual_beat.get("selected_shot_order_ids")
                # 【核心修改】如果镜头ID列表不存在或为空，则直接跳过这个视觉节拍
                if not selected_ids:
                    self.logger.warning(
                        f"叙事单元 {source_unit_num} 的场景 {visual_beat.get('source_scene_number')} "
                        "的镜头列表为空，已在时间线组装中跳过。"
                    )
                    continue

                # 3. 根据挑选出的ID，按顺序获取镜头
                selected_shots = self.db_manager.get_shots_by_order_ids(self.video_id, selected_ids)
                
                # 【新增验证】如果根据ID没有找到任何镜头实体，也跳过
                if not selected_shots:
                    self.logger.warning(
                        f"叙事单元 {source_unit_num} 的场景 {visual_beat.get('source_scene_number')} "
                        f"的镜头ID {selected_ids} 在数据库中未找到匹配项，已跳过。"
                    )
                    continue

                # 4. 将挑选出的镜头依次添加到时间线
                for shot in selected_shots:
                    shot_duration = shot["end_time"] - shot["start_time"]
                    event = {
                        "path": Path(shot["clip_url"]),
                        "timeline_start": timeline_pos,
                        "timeline_end": timeline_pos + shot_duration,
                        "timeline_duration": shot_duration,
                        "source_in": 0,
                        "source_out": shot_duration,
                        "track_index": current_video_track_index,
                        "use_original_audio": True,  # 在纯视觉阶段，默认保留所有原声
                        "ducking_info": None,  # 没有旁白，无需音频闪避
                    }
                    video_events.append(event)
                    timeline_pos += shot_duration

            # 每个叙事单元结束后，切换视频轨道以实现交错效果
            current_video_track_index = 1 - current_video_track_index

        # 5. 保存剪辑计划并生成XML
        # --- 【核心修复】为保存到数据库创建一个可序列化的副本 ---
        serializable_video_events = []
        for event in video_events:
            event_copy = event.copy()
            if isinstance(event_copy.get("path"), Path):
                event_copy["path"] = str(event_copy["path"])
            serializable_video_events.append(event_copy)
        # --- 修复结束 ---

        self.db_manager.save_stage_output(
            self.video_id,
            self.stage_number,
            self.stage_name,
            "final_cut_plan",
            {"final_cut_plan": serializable_video_events},  # 使用可序列化的副本
        )
        self.logger.info("已保存详细的纯视觉剪辑计划到数据库。")

        if not video_events:
            self.logger.error("未能构建任何有效的时间线事件。")
            return False

        # 传递原始的、包含Path对象的列表给XML生成函数
        return self._compose_final_video(video_events, [])

    @retry_on_exception()
    def _process_single_beat(
        self,
        beat: Dict[str, Any],
        beat_idx: int,
        tts_voice: str,
        temp_dir: Path,
        scenes_outline: Dict[int, Any],
        used_shot_order_ids: set,
    ) -> Optional[Dict[str, Any]]:
        """处理单个音画节拍的核心逻辑。"""
        audio_content = beat.get("audio_content")
        narration_type = beat.get("narration_type")

        audio_path, audio_duration = None, 0.0
        # 默认总是包含原声音轨，后续通过 ducking_info 控制音量
        use_original_audio = True

        if audio_content:
            if narration_type in ["NARRATOR", "INNER_MONOLOGUE"]:
                tts_client = get_tts_client()
                audio_path = temp_dir / f"tts_beat_{beat_idx + 1}_{hash(audio_content)}.mp3"
                if tts_client.generate_audio(audio_content, audio_path, voice_name=tts_voice):
                    audio_duration = video_processor.get_media_info(audio_path).get("duration", 0)
                else:
                    audio_path = None  # 生成失败
            elif narration_type == "CHARACTER_DIALOGUE":
                # 角色对白节拍，不生成TTS，但保留原声（已默认开启）
                self.logger.debug(f"节拍 {beat_idx + 1} 为角色对白，将使用原视频音频。")
            else:
                self.logger.warning(f"节拍 {beat_idx + 1} 的 narration_type '{narration_type}' 未知，将跳过音频处理。")
        else:
            self.logger.debug(f"节拍 {beat_idx + 1} 无音频内容，为纯视觉节拍，但仍会保留原声。")

        scene_num = beat.get("source_scene_number")
        if scene_num is None or scene_num not in scenes_outline:
            self.logger.warning(f"音画节拍 {beat_idx + 1} 缺少有效的源场景编号，无法匹配视频。")
            return {
                "beat_idx": beat_idx,
                "audio_path": audio_path,
                "audio_duration": audio_duration,
                "final_shots": [],
                "use_original_audio": use_original_audio,
            }

        scene_outline = scenes_outline[scene_num]
        candidate_shots = self._get_candidate_shots_for_scene(scene_outline)

        # 【核心修改】过滤掉已使用过的镜头
        available_shots = [shot for shot in candidate_shots if shot["shot_order_id"] not in used_shot_order_ids]
        if len(available_shots) < len(candidate_shots):
            self.logger.info(
                f"为节拍 {beat_idx + 1} 过滤了 {len(candidate_shots) - len(available_shots)} 个已用镜头。"
                f"可用镜头数从 {len(candidate_shots)} 减少到 {len(available_shots)}。"
            )

        # 如果过滤后没有可用镜头，则记录警告并使用原始列表（允许重复），以防万一
        if not available_shots and candidate_shots:
            self.logger.warning(f"节拍 {beat_idx + 1} 的所有候选镜头均已被使用，将允许重复使用以避免失败。")
            shots_for_refinement = candidate_shots
        else:
            shots_for_refinement = available_shots

        # 对于纯视觉节拍或角色对白节拍，script_text 传递 visual_description
        # 对于旁白节拍，script_text 传递 audio_content
        text_for_refinement = audio_content if audio_content else beat.get("visual_description", "")

        refinement_result = self._refine_rough_cut(text_for_refinement, audio_duration, shots_for_refinement)

        # 如果AI决策失败，则返回空结果
        if not refinement_result:
            self.logger.error(f"音画节拍 {beat_idx + 1} 的精剪决策失败。")
            return None

        final_shot_ids = refinement_result.selected_shot_order_ids
        justification = refinement_result.justification

        final_shots = [shot for shot in shots_for_refinement if shot["shot_order_id"] in final_shot_ids]
        if not final_shots:
            self.logger.warning(f"音画节拍 {beat_idx + 1} 未能匹配到任何镜头。")
        else:
            final_shots.sort(key=lambda x: final_shot_ids.index(x["shot_order_id"]))

        # --- 新增：清理无法JSON序列化的字段 ---
        for shot in final_shots:
            shot.pop("embedding_vector", None)
            shot.pop("text_embedding_vector", None)
            shot.pop("created_at", None)
            shot.pop("updated_at", None)
        # --- 新增结束 ---

        return {
            "beat_idx": beat_idx,
            "audio_path": str(audio_path) if audio_path else None,
            "audio_duration": audio_duration,
            "final_shots": final_shots,
            "use_original_audio": use_original_audio,
            "justification": justification,
            "original_beat_info": beat,
        }

    def _assemble_timeline_from_beats(
        self, beat_results: List[Dict[str, Any]]
    ) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """根据处理过的音画节拍结果，按顺序组装时间线。"""
        video_events, narration_audio_events = [], []
        timeline_pos, current_video_track_index = 0.0, 0  # 视频轨道索引

        for result in beat_results:
            audio_path, audio_duration, final_shots, use_original_audio = (
                result["audio_path"],
                result["audio_duration"],
                result["final_shots"],
                result["use_original_audio"],
            )

            visual_duration = sum(s.get("duration_sec", s["end_time"] - s["start_time"]) for s in final_shots)
            segment_duration = max(audio_duration, visual_duration)  # 确保整个节拍时长足够长

            # 处理旁白/TTS音频
            if audio_path:
                narration_audio_events.append(
                    {
                        "path": audio_path,
                        "timeline_start": timeline_pos,
                        "timeline_end": timeline_pos + audio_duration,
                        "timeline_duration": audio_duration,
                        "source_in": 0,
                        "source_out": audio_duration,
                    }
                )

            # 处理视频片段和其原声
            current_video_pos_in_segment = 0.0
            for shot in final_shots:
                shot_duration = shot.get("duration_sec", shot["end_time"] - shot["start_time"])

                # 视频事件
                event = {
                    "path": Path(shot["clip_url"]),
                    "timeline_start": timeline_pos + current_video_pos_in_segment,
                    "timeline_end": timeline_pos + current_video_pos_in_segment + shot_duration,
                    "timeline_duration": shot_duration,
                    "source_in": 0,
                    "source_out": shot_duration,
                    "track_index": current_video_track_index,  # 使用视频轨道索引
                    "ducking_info": {"start": timeline_pos, "end": timeline_pos + audio_duration}
                    if audio_path
                    else None,
                    "use_original_audio": use_original_audio,  # 传递是否使用原声的标记
                }
                video_events.append(event)
                current_video_pos_in_segment += shot_duration

            timeline_pos += segment_duration
            # 切换视频轨道，实现交错剪辑效果
            current_video_track_index = 1 - current_video_track_index

        return video_events, narration_audio_events

    def _get_candidate_shots_for_scene(self, scene_outline: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取一个场景的所有候选镜头详情。"""
        scene_numbers_to_resolve = scene_outline.get("candidate_scene_ids", [])
        candidate_order_ids = []

        if scene_numbers_to_resolve:
            resolved_ids = set()
            for s_num in scene_numbers_to_resolve:
                shots = self.db_manager.get_shots_by_scene_number(self.video_id, s_num)
                for shot in shots:
                    if shot.get("shot_order_id") is not None:
                        resolved_ids.add(shot["shot_order_id"])
                # 过滤掉不在当前调试场景限制内的场景
                if settings.DEBUG_MODE_SCENE_LIMIT > 0 and s_num > settings.DEBUG_MODE_SCENE_LIMIT:
                    continue
            candidate_order_ids = sorted(list(resolved_ids))

        shots = self.db_manager.get_shots_by_order_ids(self.video_id, candidate_order_ids)
        for shot in shots:
            shot["duration_sec"] = shot["end_time"] - shot["start_time"]
        return shots

    def _refine_rough_cut(
        self, script_text: str, narration_duration: float, candidate_shots: List[Dict[str, Any]]
    ) -> Optional[RoughCutRefinementResponse]:
        """调用AI进行精剪决策，返回完整的响应对象。"""
        if not candidate_shots:
            return None

        # 如果没有旁白（即这是一个原声片段），则默认使用所有候选镜头
        if not script_text:
            return RoughCutRefinementResponse(
                selected_shot_order_ids=[shot["shot_order_id"] for shot in candidate_shots],
                justification="纯视觉/对话节拍，默认使用所有候选镜头。",
            )

        cleaned_shots = [
            {
                "shot_order_id": shot["shot_order_id"],
                "duration_sec": shot["duration_sec"],
                "visual_description": shot.get("visual_description", ""),
                "dialogue": shot.get("dialogue", ""),
                "people": shot.get("people", ""),
                "setting": shot.get("setting", ""),
                "action": shot.get("action", ""),
                "emotion": shot.get("emotion", ""),
                "key_objects": shot.get("key_objects", ""),
                "shot_type": shot.get("shot_type", ""),
                "camera_angle": shot.get("camera_angle", ""),
                "camera_movement": shot.get("camera_movement", ""),
                "composition": shot.get("composition", ""),
                "lighting": shot.get("lighting", ""),
                "on_screen_text": shot.get("on_screen_text", ""),
            }
            for shot in candidate_shots
        ]

        prompt = ROUGH_CUT_REFINEMENT_PROMPT.format(
            scene_script_text=script_text,
            narration_duration=narration_duration,
            candidate_shots_json=json.dumps(cleaned_shots, ensure_ascii=False, indent=2),
            total_candidate_duration=sum(s["duration_sec"] for s in cleaned_shots),
            language=settings.SCRIPT_LANGUAGE,
        )
        try:
            result = self.editing_client.call_ai_with_tool(prompt, response_model=RoughCutRefinementResponse)
            self.logger.info(f"AI精剪决策完成。理由: {result.justification}")

            sorted_ids = sorted(result.selected_shot_order_ids)
            if sorted_ids != result.selected_shot_order_ids:
                self.logger.warning(
                    f"AI返回的精剪镜头ID列表为乱序，已自动排序。原始顺序: {result.selected_shot_order_ids}, 排序后: {sorted_ids}"
                )
                result.selected_shot_order_ids = sorted_ids
            return result
        except Exception as e:
            self.logger.error(f"AI精剪决策失败: {e}", exc_info=True)
            return None

    def _compose_final_video(
        self, video_events: List[Dict[str, Any]], narration_audio_events: List[Dict[str, Any]]
    ) -> bool:
        """根据时间线事件生成最终的FCP7 XML。"""
        video_info = self.get_video_info()
        video_hash = video_info.get("file_hash") if video_info else None
        if video_hash:
            video_output_dir = settings.OUTPUT_DIR / video_hash
            video_output_dir.mkdir(parents=True, exist_ok=True)
            xml_output_path = video_output_dir / f"{self.stage_number}-final_cut.xml"
        else:
            self.logger.warning("无法获取视频哈希值，使用后备命名方案。")
            xml_output_path = settings.OUTPUT_DIR / f"video_{self.video_id}-{self.stage_number}-final_cut.xml"
        project_title = (
            video_info.get("video_name", f"AutoCutter Project {self.video_id}")
            if video_info
            else f"AutoCutter Project {self.video_id}"
        )

        success = video_processor.generate_fcp7_xml_from_timeline(
            video_events=video_events,
            audio_events=narration_audio_events,
            output_path=xml_output_path,
            title=project_title,
        )

        if success:
            self.logger.info(f"剪辑完成！项目文件已生成: {xml_output_path}")
            return True
        else:
            self.logger.error("生成XML文件失败，请检查 utils/video_utils.py 中的错误日志。")
            return False
