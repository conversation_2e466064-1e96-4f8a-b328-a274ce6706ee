"""
阶段10：数据基础构建 (D2S Foundation)
生成作为“唯一真实之源”的主JSON上下文文件。
"""

import json
from typing import Any, Dict, Optional

from config.prompts import CHARACTER_COMPLETION_PROMPT, CREATIVE_BRIEF_GENERATION_PROMPT, RELATIONSHIP_ENRICHMENT_PROMPT
from config.schemas import CharacterCompletionResponse, DesignDocParseResponse
from config.settings import settings
from stages.base import BaseStage
from utils.ai_utils import most_advanced_ai_client


class Stage10Foundation(BaseStage):
    """阶段10：数据基础构建 (D2S Foundation)"""

    def __init__(self, db_manager, video_id):
        super().__init__(db_manager, video_id)
        # 此阶段的核心是AI解析，可以使用高级AI客户端
        self.parsing_client = most_advanced_ai_client

    @property
    def stage_number(self) -> int:
        return 10

    @property
    def stage_name(self) -> str:
        return "数据基础构建 (D2S Foundation)"

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        # 此阶段依赖于1-9阶段的数据完整性
        for i in range(1, self.stage_number):
            status = self.db_manager.get_stage_status(self.video_id, i)
            if not status or status["status"] != "completed":
                return False, f"阶段{i}尚未完成"
        return True, ""

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """根据强制级别，调度主上下文的生成或补全。"""
        existing_brief = self.db_manager.get_stage_output(self.video_id, self.stage_number, "creative_brief")

        if force_level == "full":
            self.logger.info("强制模式 'full': 清理旧的主上下文，将从头开始生成。")
            self.db_manager.clear_stage_output(self.video_id, self.stage_number, "creative_brief")
            return self._generate_from_scratch(**kwargs)

        if force_level == "soft":
            self.logger.info("--- 强制软执行模式 ---")
            if existing_brief:
                self.logger.info("发现已存在的主上下文，将仅进行角色补全迭代。")
                return self._refine_existing_brief(existing_brief)
            else:
                self.logger.info("未发现已存在的主上下文，将从头开始生成。")
                return self._generate_from_scratch(**kwargs)

        if existing_brief:
            self.logger.info("已存在主上下文，跳过。")
            return True

        # 默认行为：如果不存在，则从头开始生成
        return self._generate_from_scratch(**kwargs)

    def _enrich_relationships_from_narrative_context(self, master_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用完整的叙事上下文（序列和场景），请求LLM来丰富角色关系。
        """
        self.update_progress("正在从叙事上下文中推断和丰富角色关系...")

        # 1. 获取现有角色和叙事大纲
        characters_json = json.dumps(master_context.get("characters", []), ensure_ascii=False, indent=2)
        narrative_outline = self.db_manager.get_structured_narrative_for_scripting(self.video_id)

        if "没有可用的序列分析数据" in narrative_outline:
            self.logger.info("没有可用的序列/场景数据，跳过基于叙事上下文的关系丰富。")
            return master_context

        # 2. 创建并调用提示词
        prompt = RELATIONSHIP_ENRICHMENT_PROMPT.format(
            characters_json=characters_json,
            narrative_outline=narrative_outline,
            language=settings.SCRIPT_LANGUAGE,
        )

        try:
            self.logger.info("请求AI根据叙事大纲丰富角色关系...")
            result = self.parsing_client.call_ai_with_tool(prompt, response_model=CharacterCompletionResponse)

            if result and result.completed_characters:
                # 更新 master_context 中的角色信息
                updated_characters = [char.model_dump() for char in result.completed_characters]
                master_context["characters"] = updated_characters
                self.logger.info(f"成功丰富了 {len(updated_characters)} 个角色的关系信息。")
            else:
                self.logger.warning("AI未能返回任何丰富的角色关系信息。")

        except Exception as e:
            self.logger.error(f"AI丰富角色关系时失败: {e}", exc_info=True)

        return master_context

    def _ensure_all_narrative_characters_exist(self, master_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        核对所有在叙事场景中出现的角色是否存在于主上下文中。
        如果不存在，则记录警告，但不会自动添加。
        """
        self.update_progress("正在从叙事摘要中核对角色...")

        # 1. 从数据库获取所有场景摘要，并从中提取所有出现的角色名称
        scene_summaries = self.db_manager.get_all_scene_summaries_for_event_detection(self.video_id)
        narrative_character_names = set()
        for scene in scene_summaries:
            # 过滤掉 "未知角色 X" 这样的占位符，因为它们不是真实的角色名
            valid_names = [name for name in scene.get("characters_present", []) if not name.startswith("未知角色")]
            narrative_character_names.update(valid_names)

        if not narrative_character_names:
            self.logger.info("在场景摘要中未发现任何有效的角色名称，跳过核对。")
            return master_context

        # 2. 获取当前主上下文中已知的角色名称
        known_character_names = {char.get("name") for char in master_context.get("characters", [])}

        # 3. 找出在叙事中存在，但在主上下文中缺失的角色
        missing_names = narrative_character_names - known_character_names
        if not missing_names:
            self.logger.info("主上下文中的角色列表已包含所有叙事中出现的角色。")
            return master_context

        self.logger.warning(
            f"在叙事摘要中发现 {len(missing_names)} 个主上下文中缺失的角色: {missing_names}。"
            "这些角色将不会被自动添加，可能会影响后续步骤的完整性。"
        )

        # 4. (已移除) 不再为缺失的角色创建占位符，直接返回原始上下文。
        return master_context

    def _generate_from_scratch(self, **kwargs) -> bool:
        """从头开始生成完整的主上下文。"""
        self.update_progress("从头生成主上下文...")
        # --- 步骤 1: 初始生成 ---
        self.logger.info("正在汇总研究数据以生成初始创作说明书...")
        research_summary = self.db_manager.get_all_research_summaries(self.video_id)
        if not research_summary or research_summary == "没有可用的研究资料。":
            self.logger.error("未能从数据库获取足够的研究资料，无法生成主上下文。")
            return False

        creative_brief_input = {
            "target_audience": kwargs.get("target_audience", "未指定"),
            "visual_style": kwargs.get("visual_style", "未指定"),
            "narrative_rhythm": kwargs.get("narrative_rhythm", "未指定"),
            "target_duration": kwargs.get("target_duration", "未指定"),
            "platform": kwargs.get("platform", "未指定"),
        }

        initial_prompt = CREATIVE_BRIEF_GENERATION_PROMPT.format(
            research_summary=research_summary, **creative_brief_input, language=settings.SCRIPT_LANGUAGE
        )

        try:
            initial_result = self.parsing_client.call_ai_with_tool(
                initial_prompt, response_model=DesignDocParseResponse
            )
            master_context = initial_result.model_dump()
        except Exception as e:
            self.logger.error(f"AI生成初始创作说明书JSON失败: {e}", exc_info=True)
            return False

        # --- 【核心修改】在丰富关系前，确保所有叙事角色都存在 ---
        master_context = self._ensure_all_narrative_characters_exist(master_context)

        # --- 调用关系增强方法 ---
        master_context = self._enrich_relationships_from_narrative_context(master_context)

        # --- 步骤 2: 迭代补全 ---
        completed_context = self._iterate_and_complete_characters(master_context)

        # --- 步骤 3: 保存结果 ---
        self.db_manager.save_stage_output(
            video_id=self.video_id,
            stage_number=self.stage_number,
            stage_name=self.stage_name,
            output_type="creative_brief",
            output_data=completed_context,
        )
        self.logger.info("✅ 成功从头生成并保存了主上下文。")
        return True

    def _refine_existing_brief(self, existing_brief: Dict[str, Any]) -> bool:
        """在现有的主上下文上执行角色补全迭代。"""
        self.update_progress("补全和优化现有主上下文中的角色...")

        # --- 【核心修改】在丰富关系前，确保所有叙事角色都存在 ---
        ensured_brief = self._ensure_all_narrative_characters_exist(existing_brief)

        # 在细化现有简报时也应用基于叙事上下文的关系增强
        enriched_brief = self._enrich_relationships_from_narrative_context(ensured_brief)

        completed_context = self._iterate_and_complete_characters(enriched_brief)
        self.db_manager.save_stage_output(
            video_id=self.video_id,
            stage_number=self.stage_number,
            stage_name=self.stage_name,
            output_type="creative_brief",
            output_data=completed_context,
        )
        self.logger.info("✅ 成功补全并保存了主上下文。")
        return True

    def _iterate_and_complete_characters(self, master_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        接收一个主上下文对象，通过迭代补全其中缺失的角色，并返回更新后的对象。
        这是该阶段的核心迭代逻辑。
        """
        MAX_ITERATIONS = 3  # 设置最大迭代次数以防止无限循环
        for i in range(MAX_ITERATIONS):
            self.logger.info(f"开始第 {i + 1}/{MAX_ITERATIONS} 轮角色完整性检查...")

            # 识别已知角色和被提及的角色
            known_character_ids = {char["character_id"] for char in master_context.get("characters", [])}
            mentioned_character_ids = set()
            for char in master_context.get("characters", []):
                for rel in char.get("relationships", []):
                    mentioned_character_ids.add(rel["character_id"])

            missing_ids = mentioned_character_ids - known_character_ids

            if not missing_ids:
                self.logger.info("角色完整性检查通过，所有被提及的角色均已定义。")
                break

            self.logger.warning(f"发现未定义的角色ID: {missing_ids}。开始请求AI进行补全...")

            completion_prompt = CHARACTER_COMPLETION_PROMPT.format(
                existing_design_doc_json=json.dumps(master_context, ensure_ascii=False, indent=2),
                missing_character_ids=", ".join(missing_ids),
                language=settings.SCRIPT_LANGUAGE,
            )

            try:
                completion_result = self.parsing_client.call_ai_with_tool(
                    completion_prompt, response_model=CharacterCompletionResponse
                )
                if completion_result and completion_result.completed_characters:
                    new_characters = [char.model_dump() for char in completion_result.completed_characters]
                    master_context["characters"].extend(new_characters)
                    self.logger.info(f"成功补全了 {len(new_characters)} 个角色信息。")
                else:
                    self.logger.warning("AI未能返回任何补全的角色信息，终止迭代。")
                    break
            except Exception as e:
                self.logger.error(f"请求AI补全角色信息时失败: {e}，终止迭代。")
                break
        else:
            self.logger.warning(f"已达到最大迭代次数 ({MAX_ITERATIONS})，可能仍有角色未定义。")

        return master_context
