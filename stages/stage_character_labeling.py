"""
阶段6：角色自动标注
从外部数据源（如豆瓣）抓取演员信息，并通过人脸识别自动标注角色。
"""

import json
import re
import tempfile
from pathlib import Path
from typing import Any, Dict, List, Optional

import requests
from bs4 import BeautifulSoup, Tag
from scipy.spatial.distance import cosine

from config.settings import settings
from stages.base import BaseStage
from utils.face_utils import get_face_embedder


class Stage6CharacterLabeling(BaseStage):
    """阶段6：角色自动标注"""

    @property
    def stage_number(self) -> int:
        return 6  # ← 原 5 +1

    @property
    def stage_name(self) -> str:
        return "角色自动标注"

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        stage5_status = self.db_manager.get_stage_status(self.video_id, 5)
        if not stage5_status or stage5_status["status"] != "completed":
            return False, "阶段5（角色识别与聚类）尚未完成"
        return True, ""

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """执行角色自动标注"""
        try:
            # --- 【核心修改】获取视频哈希值以生成标准文件名 ---
            video_info = self.get_video_info()
            video_hash = video_info.get("file_hash") if video_info else None
            if video_hash:
                cast_list_path = settings.OUTPUT_DIR / f"{video_hash}-cast_list.json"
            else:
                self.logger.warning("无法获取视频哈希值，将使用基于ID的后备文件名。")
                cast_list_path = settings.OUTPUT_DIR / f"cast_list_{self.video_id}.json"

            if force_level:
                self.logger.info(f"强制模式 '{force_level}': 将重新进行所有角色匹配和建议。")
                self._clear_labeling_data()
                if force_level == "full":
                    # --- 【核心修改】使用新的文件名逻辑删除文件 ---
                    if cast_list_path.exists():
                        cast_list_path.unlink()
                        self.logger.info(f"强制模式 'full': 已删除缓存的演职员列表文件 {cast_list_path.name}")

            # 1. 从研究数据中查找豆瓣演职员页面的URL
            douban_url = self._find_douban_celebrities_url()
            if not douban_url:
                self.logger.info("在研究数据中未找到豆瓣演职员页面URL，跳过自动标注。")
                return True

            self.update_progress(f"发现豆瓣演职员页面: {douban_url}")

            # 2. 获取并解析页面内容
            celebrities = self._parse_douban_page(douban_url)
            self.logger.debug(celebrities)
            if not celebrities:
                self.logger.warning("无法从豆瓣页面解析出任何有效的演员信息。")
                return True

            # 新增：将解析出的演员列表保存到文件，供后续人工命名使用
            # --- 【核心修改】使用新的文件名逻辑保存文件 ---
            with open(cast_list_path, "w", encoding="utf-8") as f:
                json.dump(celebrities, f, ensure_ascii=False, indent=2)
            self.logger.info(f"已将解析出的演职员表保存到: {cast_list_path}")

            # 3. 获取数据库中待命名的角色
            unknown_characters = self.db_manager.get_unnamed_characters_with_embeddings(self.video_id)
            if not unknown_characters:
                self.logger.info("数据库中没有需要命名的角色。")
                return True

            self.update_progress(f"开始匹配 {len(celebrities)} 位演员与 {len(unknown_characters)} 个未知角色...")

            # 4. 逐一匹配并标注
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                for celeb in celebrities:
                    self._match_and_label_celebrity(celeb, unknown_characters, temp_path)

            self.logger.info("角色自动标注流程完成。")
            return True

        except Exception as e:
            self.logger.error(f"执行阶段 {self.stage_number} ({self.stage_name}) 时出错: {e}", exc_info=True)
            return False

    def _find_douban_celebrities_url(self) -> Optional[str]:
        """在研究数据中查找豆瓣演职员页面的URL"""
        from database.models import Research

        with self.db_manager.get_session() as session:
            research_record = (
                session.query(Research.retrieved_content)
                .filter(
                    Research.video_id == self.video_id,
                    Research.retrieved_content.like("%https://movie.douban.com/subject/%/celebrities%"),
                )
                .first()
            )
            if research_record and research_record.retrieved_content:
                match = re.search(
                    r"(https://movie\.douban\.com/subject/\d+/celebrities)", research_record.retrieved_content
                )
                if match:
                    return match.group(1)
        return None

    def _clear_labeling_data(self):
        """
        清理此视频之前的所有角色标注和建议信息。
        将所有角色的名称重置为“未知角色 X”的格式，并清除建议。
        """
        from database.models import Characters

        self.logger.info("正在将所有角色重置为未标注状态...")
        with self.db_manager.get_session() as session:
            # 按ID排序以确保每次重置的编号都一致
            characters_to_reset = (
                session.query(Characters).filter_by(video_id=self.video_id).order_by(Characters.id).all()
            )

            if not characters_to_reset:
                self.logger.info("没有需要重置的角色。")
                return

            # 重新编号，以恢复到类似阶段4刚完成时的状态
            for i, char in enumerate(characters_to_reset, 1):
                char.name = f"未知角色 {i}"
                char.suggested_name_info = None
                char.source = "clustered"  # 将来源重置为“聚类”

            session.commit()
            self.logger.info(f"已成功重置 {len(characters_to_reset)} 个角色的标注信息。")

    def _parse_douban_page(self, url: str) -> List[Dict[str, str]]:
        """解析豆瓣演职员页面，提取演员、角色名、职业和头像URL"""
        try:
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            }
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            soup = BeautifulSoup(response.text, "html.parser")

            celebrities = []
            cast_section = soup.find("h2", string=re.compile(r"演员 Cast"))
            if not cast_section:
                return []

            cast_list = cast_section.find_next("ul")
            if not cast_list or not isinstance(cast_list, Tag):
                return []

            for item in cast_list.find_all("li", class_="celebrity"):
                if not isinstance(item, Tag):
                    continue

                actor_name_tag = item.find("a", class_="name")
                role_tag = item.find("span", class_="role")
                avatar_tag = item.find("div", class_="avatar")

                if (
                    actor_name_tag
                    and isinstance(actor_name_tag, Tag)
                    and role_tag
                    and isinstance(role_tag, Tag)
                    and avatar_tag
                    and isinstance(avatar_tag, Tag)
                    and avatar_tag.has_attr("style")
                ):
                    actor_name = actor_name_tag.text.strip()
                    role_text = role_tag.text.strip()

                    # 提取角色名 (e.g., "Anna")
                    char_name_match = re.search(r"饰\s*([^)]+)", role_text)
                    character_name = char_name_match.group(1).strip() if char_name_match else "未知"

                    # 新增：提取职业 (e.g., "Actress", "Actor", "Actor/Actress")
                    role_type_match = re.search(r"(Actor/Actress|Actor|Actress)", role_text)
                    role_type = role_type_match.group(1) if role_type_match else ""

                    # 从 background-image: url(...) 中提取 URL
                    style_attr = str(avatar_tag.get("style", ""))
                    url_match = re.search(r"url\((.*?)\)", style_attr)
                    avatar_url = url_match.group(1) if url_match else None

                    if actor_name and character_name:
                        # 【核心修改】不再因为默认头像而跳过，而是将其URL置空
                        if avatar_url and "personage-default-medium.png" in avatar_url:
                            self.logger.debug(f"演员 {actor_name} 使用默认头像，将不进行人脸匹配。")
                            avatar_url = None  # 将URL置为None，但保留演员信息

                        celebrities.append(
                            {
                                "actor_name": actor_name,
                                "character_name": character_name,
                                "role_type": role_type,
                                "avatar_url": avatar_url,  # 此处可能为有效URL或None
                            }
                        )
            self.logger.info(f"成功从豆瓣页面解析出 {len(celebrities)} 位有效演员信息。")
            return celebrities
        except requests.RequestException as e:
            self.logger.error(f"请求豆瓣页面失败: {e}")
            return []
        except Exception as e:
            self.logger.error(f"解析豆瓣页面时发生未知错误: {e}", exc_info=True)
            return []

    def _match_and_label_celebrity(
        self, celeb_info: Dict[str, str], unknown_characters: List[Dict[str, Any]], temp_dir: Path
    ):
        """下载演员头像，提取特征，并与所有足够接近的未知角色进行匹配或建议。"""
        # 1. 检查并下载头像
        avatar_url = celeb_info.get("avatar_url")
        if not avatar_url:
            return  # 如果没有头像，直接跳过

        try:
            response = requests.get(avatar_url, timeout=10)
            response.raise_for_status()
            avatar_path = temp_dir / f"{celeb_info['actor_name']}.jpg"
            with open(avatar_path, "wb") as f:
                f.write(response.content)

            # 2. 提取头像的人脸嵌入
            face_embedder = get_face_embedder()
            embeddings_data = face_embedder.extract_embeddings_from_image(avatar_path)
            if not embeddings_data:
                self.logger.warning(f"无法从演员 {celeb_info['actor_name']} 的头像中提取人脸特征。")
                return

            celeb_embedding = embeddings_data[0]["embedding"]

            # --- 【核心修改】寻找所有匹配项，而不是只找最佳匹配 ---
            hard_matches = []
            suggestions = []
            min_distance_overall = float("inf")

            # 3. 遍历所有未知角色，分类为硬性匹配、建议或不匹配
            for char in unknown_characters:
                distance = cosine(celeb_embedding, char["embedding"])
                min_distance_overall = min(min_distance_overall, distance)

                if distance < settings.CHARACTER_MATCHING_THRESHOLD:
                    hard_matches.append({"char_id": char["id"], "distance": distance})
                elif distance < settings.CHARACTER_SUGGESTION_THRESHOLD:
                    suggestions.append({"char_id": char["id"], "distance": distance})

            # 4. 根据匹配结果执行操作
            if hard_matches:
                # 如果有硬性匹配，则命名所有匹配的角色
                self.logger.info(f"为演员 {celeb_info['actor_name']} 找到了 {len(hard_matches)} 个硬性匹配。")
                # --- 【核心修改】开始 ---
                role_type_display = f"{celeb_info.get('role_type', '')}, " if celeb_info.get('role_type') else ""
                new_name = f"{celeb_info['character_name']} ({role_type_display}演员:{celeb_info['actor_name']})"
                # --- 【核心修改】结束 ---

                for match in hard_matches:
                    self.db_manager.update_character_name(match["char_id"], new_name, source="matched")
                    self.update_progress(
                        f"匹配成功: {new_name} (角色ID: {match['char_id']}, 距离: {match['distance']:.4f})"
                    )

                # 从未知角色列表中移除所有已匹配的角色
                matched_ids = {match["char_id"] for match in hard_matches}
                unknown_characters[:] = [char for char in unknown_characters if char["id"] not in matched_ids]

            elif suggestions:
                # 如果没有硬性匹配，但有建议，则为所有建议的角色添加建议
                self.logger.info(f"为演员 {celeb_info['actor_name']} 找到了 {len(suggestions)} 个建议匹配。")
                # --- 【核心修改】开始 ---
                role_type_display = f"{celeb_info.get('role_type', '')}, " if celeb_info.get('role_type') else ""
                suggested_name = f"{celeb_info['character_name']} ({role_type_display}演员:{celeb_info['actor_name']})"
                # --- 【核心修改】结束 ---

                for sugg in suggestions:
                    suggestion_data = {"name": suggested_name, "distance": round(sugg["distance"], 4)}
                    self.db_manager.update_character_suggestion(sugg["char_id"], suggestion_data, source="suggested")
                    self.update_progress(
                        f"为角色 {sugg['char_id']} 添加建议: {suggested_name} (距离: {sugg['distance']:.4f})"
                    )
            else:
                # 如果没有任何匹配或建议
                self.update_progress(
                    f"演员 {celeb_info['actor_name']} 未找到足够接近的匹配 (最近距离: {min_distance_overall:.4f})"
                )

        except Exception as e:
            self.logger.error(f"处理演员 {celeb_info['actor_name']} 时出错: {e}")
