"""
阶段7：场景聚合 (Shots to Scenes)
将微观的镜头(Shots)聚合成具有叙事意义的场景(Scenes)。
"""

from typing import Any, Dict, List, Optional

from sqlalchemy.orm import joinedload

from config.prompts import ORPHAN_SHOT_FIX_PROMPT, SCENE_GROUPING_PROMPT
from config.schemas import OrphanFixDecision, SceneGroupingResponse
from config.settings import settings
from database.models import Scenes, Shots
from stages.base import BaseStage
from utils.ai_utils import ai_client


class Stage7SceneGrouping(BaseStage):
    """阶段7：场景聚合"""

    def __init__(self, db_manager, video_id):
        super().__init__(db_manager, video_id)
        self.grouping_client = ai_client

    @property
    def stage_number(self) -> int:
        return 7

    @property
    def stage_name(self) -> str:
        return "场景聚合 (Shots to Scenes)"  # 更改为英文以保持一致性

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        stage6_status = self.db_manager.get_stage_status(self.video_id, 6)
        if not stage6_status or stage6_status["status"] != "completed":
            return False, "阶段6（角色自动标注）尚未完成"
        return True, ""

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """执行场景聚合，采用动态分块策略以提高边界准确性。"""
        # --- 【核心改动】新增：检查阶段是否已完成，并据此决定soft模式的行为 ---
        status_record = self.db_manager.get_stage_status(self.video_id, self.stage_number)
        is_already_completed = status_record and status_record.get("status") == "completed"

        # --- 新的 soft force 逻辑 ---
        if force_level == "soft" and is_already_completed:
            self.logger.info("--- 强制软执行模式 ---")
            self.logger.info(f"阶段{self.stage_number}已完成，将仅检查并修复孤儿镜头。")

            # 确保所有镜头都有顺序ID，以防万一
            shots = self.db_manager.get_shots_with_character_names_for_scene_grouping(self.video_id)
            if not any(shot.get("shot_order_id") for shot in shots):
                self.logger.info("镜头缺少顺序ID，正在重新分配...")
                self._assign_shot_order_ids(shots)

            orphan_shots = self._get_orphan_shots()
            if not orphan_shots:
                self.logger.info("验证通过，没有发现孤儿镜头。")
                return True

            self.logger.warning(f"发现 {len(orphan_shots)} 个孤儿镜头，开始修复流程...")
            self._fix_orphan_shots(orphan_shots)
            self.logger.info("孤儿镜头修复流程完成。")
            return True

        # --- 原有的完整执行逻辑（适用于首次运行或 --force full）---
        # 只要不是 "soft force on completed"，就执行完整流程
        if force_level:
            self._clear_data()

        shots = self.db_manager.get_shots_with_character_names_for_scene_grouping(self.video_id)
        shots.sort(key=lambda x: x["start_time"])
        if not shots:
            self.logger.info("没有已分析的镜头(Shots)可供聚合，跳过。")
            return True

        self._assign_shot_order_ids(shots)
        # ... (后续的完整聚合逻辑保持不变)
        self.update_progress(f"开始将 {len(shots)} 个镜头动态聚合为场景...")

        CHUNK_SIZE = 100
        all_raw_scenes = []
        shot_order_id_to_index_map = {shot["shot_order_id"]: i for i, shot in enumerate(shots)}
        start_index = 0
        chunk_num = 0

        while start_index < len(shots):
            chunk_num += 1
            chunk_shots = shots[start_index : start_index + CHUNK_SIZE]
            if not chunk_shots:
                break

            self.update_progress(f"处理动态镜头块 {chunk_num} (从镜头索引 {start_index} 开始)...")
            self.logger.info(f"正在处理从索引 {start_index} 开始的 {len(chunk_shots)} 个镜头...")

            # --- 对话显示逻辑 (保持不变) ---
            shot_descriptions = []
            last_dialogue = None
            for shot in chunk_shots:
                dialogue_text = (shot.get("dialogue") or "").strip()
                dialogue_display = ""

                if dialogue_text:
                    if dialogue_text == last_dialogue:
                        dialogue_display = "对白: (同上)"
                    else:
                        dialogue_display = f"对白: {dialogue_text}"
                        last_dialogue = dialogue_text
                else:
                    dialogue_display = "对白: (无)"
                    last_dialogue = None

                # 【核心修改】构建更详细的镜头描述字符串
                shot_desc = (
                    f"Shot {shot['shot_order_id']} ({shot['start_time']:.1f}s-{shot['end_time']:.1f}s): "
                    f"环境: {shot.get('setting', '')}, 人物: {shot.get('people', '')}, "
                    f"角色: {shot.get('character_names_str') or '无'}, "
                    f"动作: {shot.get('action', '')}, 情感: {shot.get('emotion', '')}, "
                    f"画面文字: {shot.get('on_screen_text', '')}, "
                    f"镜头语言: [{shot.get('shot_type', '')}, {shot.get('camera_angle', '')}, {shot.get('camera_movement', '')}], "
                    f"{dialogue_display}"
                )
                shot_descriptions.append(shot_desc)

            prompt = SCENE_GROUPING_PROMPT.format(
                shot_descriptions="\n".join(shot_descriptions),
                language=settings.SCRIPT_LANGUAGE,
            )
            prompt = (
                "重要提示：在你的回答中，请使用我们提供的'Shot'后面的数字（例如 Shot 1, Shot 2）作为 start_shot_id 和 end_shot_id。\n\n"
                + prompt
            )

            try:
                chunk_result = self.grouping_client.call_ai_with_tool(prompt, response_model=SceneGroupingResponse)
                raw_chunk_scenes = [scene.model_dump() for scene in chunk_result.scenes]

                chunk_scenes = []
                for scene_dict in raw_chunk_scenes:
                    start_order_id = scene_dict.get("start_shot_id")
                    end_order_id = scene_dict.get("end_shot_id")

                    if start_order_id is None or end_order_id is None or start_order_id > end_order_id:
                        self.logger.warning(f"AI返回了无效的场景顺序ID范围: {start_order_id}-{end_order_id}，已跳过。")
                        continue

                    scene_shots = [s for s in chunk_shots if start_order_id <= s["shot_order_id"] <= end_order_id]
                    if not scene_shots:
                        continue

                    scene_dict["shot_ids"] = [s["id"] for s in scene_shots]
                    chunk_scenes.append(scene_dict)

                all_raw_scenes.extend(chunk_scenes)

                if not chunk_scenes:
                    self.logger.warning("当前块未返回任何场景，将按固定步长前进。")
                    start_index += CHUNK_SIZE
                    continue

                current_last_shot_index = start_index + len(chunk_shots) - 1
                if current_last_shot_index >= len(shots) - 1:
                    self.logger.info("已处理到最后一个镜头，聚合循环结束。")
                    break

                last_scene = chunk_scenes[-1]
                second_last_scene = chunk_scenes[-2] if len(chunk_scenes) > 1 else None
                num_shots_in_last_scene = len(last_scene.get("shot_ids", []))

                if num_shots_in_last_scene <= 5 and second_last_scene:
                    start_shot_order_id = min(
                        s["shot_order_id"] for s in chunk_shots if s["id"] in second_last_scene["shot_ids"]
                    )
                    self.logger.info(
                        f"最后一个场景镜头数({num_shots_in_last_scene})<=5，从倒数第二个场景(起始顺序ID: {start_shot_order_id})开始下一轮。"
                    )
                else:
                    start_shot_order_id = min(
                        s["shot_order_id"] for s in chunk_shots if s["id"] in last_scene["shot_ids"]
                    )
                    self.logger.info(
                        f"最后一个场景镜头数({num_shots_in_last_scene})>5或它是唯一场景，从该场景(起始顺序ID: {start_shot_order_id})开始下一轮。"
                    )

                next_start_index = shot_order_id_to_index_map.get(start_shot_order_id)
                if next_start_index is None or next_start_index <= start_index:
                    self.logger.warning(
                        f"无法确定有效的下一个起始索引(计算出: {next_start_index})，将按固定步长前进以避免死循环。"
                    )
                    start_index += CHUNK_SIZE
                else:
                    start_index = next_start_index

            except Exception as e:
                self.logger.error(f"AI场景聚合失败 (块 {chunk_num}): {e}。将按固定步长前进。", exc_info=True)
                start_index += CHUNK_SIZE
                continue

        try:
            final_scenes_raw = self._merge_scenes(all_raw_scenes, shots)
            # 在保存前，先在数据库中创建初步的场景记录，以便获取ID
            self._save_grouped_scenes(final_scenes_raw, shots, assign_shots=False)

            # 获取刚刚创建的、带有数据库ID的场景列表
            preliminary_scenes = self._get_preliminary_scenes_for_refinement()

            # --- 【核心修改】调用新的边界优化函数 ---
            final_scenes = self._refine_scene_boundaries(preliminary_scenes)

            # 使用优化后的场景列表，重新保存并正式关联镜头
            self._save_final_scenes_and_associations(final_scenes, shots)
            self.logger.info(f"场景聚合完成，最终生成了 {len(final_scenes)} 个场景。")

            self.update_progress("验证并修复孤儿镜头...")
            orphan_shots = self._get_orphan_shots()
            if not orphan_shots:
                self.logger.info("验证通过，没有发现孤儿镜头。")
                return True

            self.logger.warning(f"发现 {len(orphan_shots)} 个孤儿镜头，开始二次修复流程...")
            self._fix_orphan_shots(orphan_shots)
            self.logger.info("场景聚合及修复流程全部完成。")
            return True

        except Exception as e:
            self.logger.error(f"执行阶段 {self.stage_number} ({self.stage_name}) 时出错: {e}", exc_info=True)
            return False

    def _are_scenes_mergeable(self, prev_scene: Dict[str, Any], current_scene: Dict[str, Any]) -> bool:
        """
        判断两个相邻的短场景是否应该合并。
        决策依据：角色重叠度、时间连续性。
        """
        # 1. 检查时间是否连续
        time_gap = current_scene["start_time"] - prev_scene["end_time"]
        if time_gap > 1.0:  # 如果场景间隙超过1秒，则不合并
            return False

        # 2. 检查角色重叠度 (使用Jaccard相似度)
        prev_chars = set(prev_scene.get("character_ids", []))
        current_chars = set(current_scene.get("character_ids", []))

        # 如果两个场景都没有角色，或者一个有角色一个没有，则不轻易合并
        if not prev_chars and not current_chars:
            # 两者都无角色，可能可以合并，继续看其他条件
            pass
        elif not prev_chars or not current_chars:
            return False

        intersection = len(prev_chars.intersection(current_chars))
        union = len(prev_chars.union(current_chars))
        jaccard_similarity = intersection / union if union > 0 else 0

        # 如果角色重叠度低于50%，则不合并
        if jaccard_similarity < 0.5:
            return False

        # 默认：如果时间连续且角色重叠度高，则可以合并
        return True

    def _merge_scene_data(self, prev_scene: Dict[str, Any], current_scene: Dict[str, Any]) -> Dict[str, Any]:
        """合并两个场景的数据字典。"""
        # 合并 shot_ids
        merged_shot_ids = prev_scene.get("shot_ids", []) + current_scene.get("shot_ids", [])

        # 更新其他元数据
        prev_scene["shot_ids"] = merged_shot_ids
        # end_shot_id 和 end_time 将在 _save_grouped_scenes 中根据实际镜头重新计算，此处无需更新
        # 摘要和目的也暂时保留前一个场景的，因为它们将在下一阶段被重新分析

        self.logger.info(
            f"智能合并：场景 (ID: {current_scene.get('id')}) 已合并到前一个场景 (ID: {prev_scene.get('id')})。"
        )
        return prev_scene

    def _refine_scene_boundaries(self, scenes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        场景后处理：智能合并过短且内容相似的场景。
        """
        if len(scenes) < 2:
            return scenes

        self.update_progress("智能优化场景边界：合并过短的相似场景...")

        # 获取包含角色信息的完整场景数据
        scenes_with_chars = self.db_manager.get_all_scenes_with_characters(self.video_id)
        scenes_map = {s["id"]: s for s in scenes_with_chars}

        refined_scenes = []

        # 使用迭代器安全地处理列表
        scene_iterator = iter(scenes)

        prev_scene_data = next(scene_iterator, None)
        if prev_scene_data is None:
            return []

        refined_scenes.append(prev_scene_data)

        for current_scene_data in scene_iterator:
            prev_scene = refined_scenes[-1]

            # 获取完整的场景上下文信息
            prev_scene_full = scenes_map.get(prev_scene["id"])
            current_scene_full = scenes_map.get(current_scene_data["id"])

            if not prev_scene_full or not current_scene_full:
                refined_scenes.append(current_scene_data)
                continue

            # 定义“过短”场景的阈值
            is_prev_short = (
                len(prev_scene.get("shot_ids", [])) < 3 and (prev_scene["end_time"] - prev_scene["start_time"]) < 10
            )
            is_current_short = (
                len(current_scene_data.get("shot_ids", [])) < 3
                and (current_scene_data["end_time"] - current_scene_data["start_time"]) < 10
            )

            # 只有当两个场景中至少一个很短时，才考虑合并
            if (is_prev_short or is_current_short) and self._are_scenes_mergeable(prev_scene_full, current_scene_full):
                # 将当前场景合并到前一个场景
                refined_scenes[-1] = self._merge_scene_data(prev_scene, current_scene_data)
            else:
                refined_scenes.append(current_scene_data)

        if len(refined_scenes) < len(scenes):
            self.logger.info(f"场景边界优化完成，场景数量从 {len(scenes)} 减少到 {len(refined_scenes)}。")

        return refined_scenes

    def _assign_shot_order_ids(self, shots: List[Dict[str, Any]]):
        """为所有镜头分配并持久化一个从1开始的顺序ID。"""
        self.logger.info(f"正在为 {len(shots)} 个镜头分配顺序ID...")
        updates = []
        for i, shot in enumerate(shots):
            order_id = i + 1
            shot["shot_order_id"] = order_id
            updates.append({"id": shot["id"], "shot_order_id": order_id})

        if updates:
            with self.db_manager.get_session() as session:
                session.bulk_update_mappings(Shots.__mapper__, updates)
            self.logger.info("镜头顺序ID分配并保存完成。")

    def _clear_data(self):
        """清理此阶段及后续阶段的数据"""
        self.logger.info("强制模式: 正在清理场景聚合及后续序列分析的数据...")
        with self.db_manager.get_session() as session:
            from database.models import StorySequence

            session.query(StorySequence).filter_by(video_id=self.video_id).delete()
            session.query(Scenes).filter_by(video_id=self.video_id).delete()
            session.query(Shots).filter_by(video_id=self.video_id).update({"scene_id": None})

    def _merge_scenes(self, raw_scenes: List[Dict[str, Any]], all_shots: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """合并来自动态块的场景列表，并解决重复问题。"""
        if not raw_scenes:
            return []
        self.logger.info(f"开始合并 {len(raw_scenes)} 个原始场景分组...")

        shot_to_scene_map: Dict[int, Dict[str, Any]] = {}
        for scene in raw_scenes:
            for shot_id in scene.get("shot_ids", []):
                shot_to_scene_map[shot_id] = scene

        unique_scenes_dict: Dict[int, Dict[str, Any]] = {id(s): s for s in shot_to_scene_map.values()}
        unique_scenes = list(unique_scenes_dict.values())

        shots_map = {shot["id"]: shot for shot in all_shots}

        def get_scene_start_time(scene: Dict[str, Any]) -> float:
            shot_ids = scene.get("shot_ids")
            if not shot_ids:
                return float("inf")
            first_shot_id = shot_ids[0]
            first_shot_info = shots_map.get(first_shot_id)
            if first_shot_info:
                return first_shot_info.get("start_time", float("inf"))
            self.logger.warning(f"无法找到场景的第一个镜头ID {first_shot_id} 的信息，该场景将被排到最后。")
            return float("inf")

        final_scenes = sorted(unique_scenes, key=get_scene_start_time)
        self.logger.info(f"合并后得到 {len(final_scenes)} 个唯一的最终场景。")
        return final_scenes

    def _get_orphan_shots(self) -> List[Dict[str, Any]]:
        """从数据库中查询所有 scene_id 为 NULL 的镜头，并按时间排序。"""
        all_shots = self.db_manager.get_shots_with_character_names_for_scene_grouping(self.video_id)
        orphan_shots = [shot for shot in all_shots if shot.get("scene_id") is None]
        orphan_shots.sort(key=lambda x: x["start_time"])
        return orphan_shots

    def _fix_orphan_shots(self, orphan_shots: List[Dict[str, Any]]):
        """使用新的智能修复逻辑处理孤儿镜头。"""
        if not orphan_shots:
            return

        with self.db_manager.get_session() as session:
            # 【核心改动】: 将所有操作都放在同一个会话中

            # 步骤1: 获取所有场景并预加载其镜头
            all_scenes = (
                session.query(Scenes)
                .options(joinedload(Scenes.shots))
                .filter_by(video_id=self.video_id)
                .order_by(Scenes.scene_number)
                .all()
            )
            if not all_scenes:
                self.logger.warning("无法获取已聚合的场景，无法修复孤儿镜头。")
                return

            # 步骤2: 构建 镜头顺序ID -> 场景对象 的映射
            # 因为所有对象都在同一个会话中，所以可以安全地访问 .shots
            order_id_to_scene_map = {
                shot.shot_order_id: scene
                for scene in all_scenes
                for shot in scene.shots
                if shot.shot_order_id is not None
            }

            # 步骤3: 遍历并修复孤儿镜头
            for orphan_shot_data in orphan_shots:
                orphan_order_id = orphan_shot_data.get("shot_order_id")
                if orphan_order_id is None:
                    self.logger.warning(f"孤儿镜头 ID {orphan_shot_data['id']} 缺少顺序ID，无法修复。")
                    continue

                self.update_progress(f"修复孤儿镜头 (顺序ID: {orphan_order_id})...")
                preceding_scene_obj = order_id_to_scene_map.get(orphan_order_id - 1)
                succeeding_scene_obj = order_id_to_scene_map.get(orphan_order_id + 1)

                if not preceding_scene_obj or not succeeding_scene_obj:
                    self.logger.warning(
                        f"孤儿镜头 (顺序ID: {orphan_order_id}) 缺少前置或后置场景，无法自动修复，跳过。"
                    )
                    continue

                if preceding_scene_obj.id == succeeding_scene_obj.id:
                    shot_to_update = session.query(Shots).filter_by(id=orphan_shot_data["id"]).first()
                    if shot_to_update:
                        shot_to_update.scene_id = preceding_scene_obj.id
                        self.logger.info(
                            f"决策: 镜头 (顺序ID: {orphan_order_id}) 已并入其前后连接的同一个场景 (ID: {preceding_scene_obj.id})。"
                        )
                        order_id_to_scene_map[orphan_order_id] = preceding_scene_obj
                    continue

                def format_shot_desc(shot_obj):
                    # 【核心修改】提供更完整的镜头描述
                    return (
                        f"Shot {shot_obj.shot_order_id} ({shot_obj.start_time:.1f}s-{shot_obj.end_time:.1f}s): "
                        f"环境: {shot_obj.setting}, 人物: {shot_obj.people}, "
                        f"动作: {shot_obj.action}, 情感: {shot_obj.emotion}, "
                        f"画面文字: {shot_obj.on_screen_text}, "
                        f"镜头语言: [{shot_obj.shot_type}, {shot_obj.camera_angle}, {shot_obj.camera_movement}], "
                        f"对白: {shot_obj.dialogue}"
                    )

                orphan_shot_obj = session.query(Shots).filter_by(id=orphan_shot_data["id"]).one_or_none()
                if not orphan_shot_obj:
                    self.logger.error(f"数据库中未找到孤儿镜头 {orphan_shot_data['id']}，跳过。")
                    continue

                # 确保前后场景对象也附加到当前会话中
                session.add(preceding_scene_obj)
                session.add(succeeding_scene_obj)

                def get_sort_key(shot_obj):
                    return shot_obj.shot_order_id if shot_obj.shot_order_id is not None else float("inf")

                preceding_shots_desc = "\n".join(
                    [format_shot_desc(s) for s in sorted(preceding_scene_obj.shots, key=get_sort_key)]
                )
                orphan_shot_desc = format_shot_desc(orphan_shot_obj)
                succeeding_shots_desc = "\n".join(
                    [format_shot_desc(s) for s in sorted(succeeding_scene_obj.shots, key=get_sort_key)]
                )

                prompt = ORPHAN_SHOT_FIX_PROMPT.format(
                    preceding_scene_shots=preceding_shots_desc,
                    orphan_shot=orphan_shot_desc,
                    succeeding_scene_shots=succeeding_shots_desc,
                    language=settings.SCRIPT_LANGUAGE,
                )

                try:
                    decision_result = self.grouping_client.call_ai_with_tool(prompt, response_model=OrphanFixDecision)
                    decision = decision_result.decision
                    target_scene = None
                    if decision == "preceding":
                        target_scene = preceding_scene_obj
                        self.logger.info(
                            f"决策: 镜头 (顺序ID: {orphan_order_id}) 已并入前一个场景 (编号: {target_scene.scene_number})。"
                        )
                    elif decision == "succeeding":
                        target_scene = succeeding_scene_obj
                        self.logger.info(
                            f"决策: 镜头 (顺序ID: {orphan_order_id}) 已并入后一个场景 (编号: {target_scene.scene_number})。"
                        )

                    if target_scene:
                        orphan_shot_obj.scene_id = target_scene.id
                        order_id_to_scene_map[orphan_order_id] = target_scene
                    elif decision == "new_scene":
                        new_scene = Scenes(
                            video_id=self.video_id,
                            scene_number=-1,  # 临时编号，后续会重新计算
                            start_time=orphan_shot_obj.start_time,
                            end_time=orphan_shot_obj.end_time,
                            summary=f"孤儿镜头 {orphan_shot_obj.shot_order_id} 独立形成的场景。",
                            narrative_purpose="孤立的叙事单元",
                            status="completed",
                        )
                        session.add(new_scene)
                        orphan_shot_obj.scene = new_scene
                        session.flush()
                        self.logger.info(
                            f"决策: 镜头 (顺序ID: {orphan_order_id}) 已自成一个新场景 (临时DB ID: {new_scene.id})。"
                        )
                        order_id_to_scene_map[orphan_order_id] = new_scene
                    else:
                        self.logger.warning(
                            f"AI为孤儿镜头 (顺序ID: {orphan_order_id}) 返回了无效决策: {decision}，跳过。"
                        )
                except Exception as e:
                    self.logger.error(f"修复孤儿镜头 (顺序ID: {orphan_order_id}) 时AI决策失败: {e}", exc_info=True)
                    continue

    def _save_grouped_scenes(
        self, scenes_data: List[Dict[str, Any]], all_shots: List[Dict[str, Any]], assign_shots: bool = True
    ):
        """将AI返回的场景分组数据存入数据库。"""
        shots_map = {shot["id"]: shot for shot in all_shots}
        with self.db_manager.get_session() as session:
            # 首先清理旧的场景数据，确保从干净的状态开始
            session.query(Scenes).filter_by(video_id=self.video_id).delete()
            session.query(Shots).filter_by(video_id=self.video_id).update({"scene_id": None})
            session.flush()

            for i, scene_info in enumerate(scenes_data):
                shot_ids = scene_info.get("shot_ids", [])
                if not shot_ids:
                    continue

                contained_shots = [shots_map[sid] for sid in shot_ids if sid in shots_map]
                if not contained_shots:
                    continue

                start_time = min(s["start_time"] for s in contained_shots)
                end_time = max(s["end_time"] for s in contained_shots)

                new_scene = Scenes(
                    video_id=self.video_id,
                    scene_number=i + 1,  # 临时编号
                    start_time=start_time,
                    end_time=end_time,
                    summary=scene_info.get("summary"),
                    narrative_purpose=scene_info.get("narrative_purpose"),
                    status="completed",
                )
                session.add(new_scene)

                if assign_shots:
                    # 如果需要，立即关联镜头
                    session.flush()  # 获取 new_scene.id
                    shot_db_ids = [s["id"] for s in contained_shots]
                    session.query(Shots).filter(Shots.id.in_(shot_db_ids)).update({"scene_id": new_scene.id})

            self.logger.info(f"已初步将 {len(scenes_data)} 个聚合场景保存到数据库。")

    def _get_preliminary_scenes_for_refinement(self) -> List[Dict[str, Any]]:
        """获取刚保存的、用于优化的场景列表，包含数据库ID。"""
        with self.db_manager.get_session() as session:
            scenes = session.query(Scenes).filter_by(video_id=self.video_id).order_by(Scenes.start_time).all()

            # 为了能在 refine 逻辑中使用，我们需要模拟 `shot_ids` 字段
            results = []
            for scene in scenes:
                scene_dict = {c.name: getattr(scene, c.name) for c in scene.__table__.columns}
                scene_dict["shot_ids"] = [shot.id for shot in scene.shots]
                results.append(scene_dict)
            return results

    def _save_final_scenes_and_associations(self, final_scenes: List[Dict[str, Any]], all_shots: List[Dict[str, Any]]):
        """保存最终优化后的场景，并正确关联镜头、重新编号。"""
        shots_map = {shot["id"]: shot for shot in all_shots}
        with self.db_manager.get_session() as session:
            # 1. 获取所有现存场景的ID，用于删除那些被合并掉的场景
            existing_scene_ids = {s.id for s in session.query(Scenes.id).filter_by(video_id=self.video_id).all()}
            final_scene_ids = {s["id"] for s in final_scenes}
            ids_to_delete = existing_scene_ids - final_scene_ids

            if ids_to_delete:
                session.query(Scenes).filter(Scenes.id.in_(ids_to_delete)).delete(synchronize_session=False)
                self.logger.info(f"已从数据库中删除 {len(ids_to_delete)} 个被合并的场景。")

            # 2. 更新最终的场景列表，重新计算时间、编号，并关联镜头
            for i, scene_info in enumerate(final_scenes):
                shot_ids = scene_info.get("shot_ids", [])
                if not shot_ids:
                    continue

                contained_shots = [shots_map[sid] for sid in shot_ids if sid in shots_map]
                if not contained_shots:
                    continue

                start_time = min(s["start_time"] for s in contained_shots)
                end_time = max(s["end_time"] for s in contained_shots)

                # 更新场景信息
                session.query(Scenes).filter_by(id=scene_info["id"]).update(
                    {
                        "scene_number": i + 1,  # 重新编号
                        "start_time": start_time,
                        "end_time": end_time,
                    }
                )

                # 关联镜头
                session.query(Shots).filter(Shots.id.in_(shot_ids)).update({"scene_id": scene_info["id"]})

            self.logger.info(f"已将 {len(final_scenes)} 个最终场景及其关联镜头更新到数据库。")
