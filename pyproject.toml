[project]
name = "auto-cutter"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10,<3.13"
dependencies = [
    "numpy<2",
    "openai>=1.90.0",
    "opencv-python>=*********",
    "python-dotenv>=1.1.0",
    "requests>=2.32.4",
    "scenedetect[opencv]>=0.6.6",
    "tavily-python>=0.7.8",
    "tos>=2.8.4",
    "psycopg2-binary>=2.9.9",
    "sqlalchemy>=2.0",
    "alembic>=1.13",
    "beautifulsoup4>=4.13.4",
    "scikit-learn>=1.7.0",
    "edge-tts>=7.0.2",
    "soundfile>=0.13.1",
    "demucs>=4.0.1",
    "mlx-whisper>=0.4.2",
    "pysubs2>=1.8.0",
    "facenet-pytorch>=2.6.0",
    "torchvision>=0.17.2",
    "pydantic>=2.11.7",
]


[[tool.uv.index]]
name = "tsinghua"
url = "https://pypi.tuna.tsinghua.edu.cn/simple"

[dependency-groups]
dev = [
    "pyright>=1.1.403",
]
