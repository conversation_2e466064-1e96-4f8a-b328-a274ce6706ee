services:
    alist:
        restart: always
        volumes:
          - './alistdata:/opt/alist/data'
          - './aria2:/opt/alist/data/temp/aria2'
          - './qbittorrent:/opt/alist/data/temp/qbittorrent'
        ports:
          - '5244:5244'
        environment:
          - PUID=0
          - PGID=0
          - UMASK=022
        container_name: alist
        image: 'xhofe/alist-aria2:latest'
