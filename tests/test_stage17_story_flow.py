#!/usr/bin/env python3
"""
Stage17 Story Flow测试脚本
测试增强后的唯一性检查和数据验证功能
"""

import sys
import os
from unittest.mock import Mock, patch


# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from stages.stage_d2s_rewriter_flow import Stage17StoryFlow
from config.schemas import StoryFlowItem, StoryFlowResponse


class TestStage17StoryFlow:
    """测试Stage17StoryFlow类的增强功能"""

    def setup_method(self):
        """测试前的设置"""
        self.db_manager = Mock()
        self.video_id = 1
        self.stage = Stage17StoryFlow(self.db_manager, self.video_id)

        # Mock logger
        self.stage.logger = Mock()

    def test_validate_unique_narrative_unit_numbers_valid(self):
        """测试有效的唯一性检查"""
        story_flow = [
            {"narrative_unit_number": 1, "reasoning": "开场"},
            {"narrative_unit_number": 2, "reasoning": "发展"},
            {"narrative_unit_number": 3, "reasoning": "高潮"},
        ]

        result = self.stage._validate_unique_narrative_unit_numbers(story_flow)
        assert result is True

    def test_validate_unique_narrative_unit_numbers_duplicate(self):
        """测试重复编号的检查"""
        story_flow = [
            {"narrative_unit_number": 1, "reasoning": "开场"},
            {"narrative_unit_number": 2, "reasoning": "发展"},
            {"narrative_unit_number": 1, "reasoning": "重复"},  # 重复编号
        ]

        result = self.stage._validate_unique_narrative_unit_numbers(story_flow)
        assert result is False
        # 验证是否记录了错误日志
        self.stage.logger.error.assert_called()

    def test_validate_unique_narrative_unit_numbers_empty(self):
        """测试空列表的检查"""
        story_flow = []
        result = self.stage._validate_unique_narrative_unit_numbers(story_flow)
        assert result is True

    def test_validate_story_flow_integrity_valid(self):
        """测试有效的完整性检查"""
        story_flow = [
            {"narrative_unit_number": 1, "reasoning": "开场"},
            {"narrative_unit_number": 2, "reasoning": "发展"},
        ]

        story_outline_data = {
            "story_outline": [
                {"narrative_unit_number": 1, "summary": "开场场景"},
                {"narrative_unit_number": 2, "summary": "发展场景"},
                {"narrative_unit_number": 3, "summary": "高潮场景"},
            ]
        }

        result = self.stage._validate_story_flow_integrity(story_flow, story_outline_data)
        assert result is True

    def test_validate_story_flow_integrity_invalid(self):
        """测试无效的完整性检查"""
        story_flow = [
            {"narrative_unit_number": 1, "reasoning": "开场"},
            {"narrative_unit_number": 4, "reasoning": "不存在的场景"},  # 不在大纲中的编号
        ]

        story_outline_data = {
            "story_outline": [
                {"narrative_unit_number": 1, "summary": "开场场景"},
                {"narrative_unit_number": 2, "summary": "发展场景"},
            ]
        }

        result = self.stage._validate_story_flow_integrity(story_flow, story_outline_data)
        assert result is False
        # 验证是否记录了错误日志
        self.stage.logger.error.assert_called()

    def test_execute_with_duplicate_numbers(self):
        """测试执行时发现重复编号的处理"""
        # Mock数据库返回包含重复编号的数据
        duplicate_data = {
            "story_flow": [
                {"narrative_unit_number": 1, "reasoning": "开场"},
                {"narrative_unit_number": 2, "reasoning": "发展"},
                {"narrative_unit_number": 1, "reasoning": "重复"},  # 重复编号
            ]
        }

        self.db_manager.get_stage_output.return_value = duplicate_data

        # Mock AI客户端
        self.stage.flow_client = Mock()

        # Mock前置条件检查
        self.db_manager.get_stage_status.return_value = {"status": "completed"}

        # 执行测试
        result = self.stage.execute()

        # 验证清理了旧数据并尝试重新生成
        assert self.db_manager.clear_stage_output.called

    def test_execute_with_valid_data(self):
        """测试执行时处理有效数据"""
        # Mock数据库返回有效数据
        valid_data = {
            "story_flow": [
                {"narrative_unit_number": 1, "reasoning": "开场"},
                {"narrative_unit_number": 2, "reasoning": "发展"},
            ]
        }

        self.db_manager.get_stage_output.return_value = valid_data

        # 执行测试
        result = self.stage.execute()

        # 验证直接返回了有效数据
        assert result is True
        assert self.stage.logger.info.called

    @patch("stages.stage_d2s_rewriter_flow.STORY_FLOW_ARRANGEMENT_PROMPT")
    def test_execute_ai_generation_with_validation(self, mock_prompt):
        """测试AI生成后验证功能"""
        # Mock没有缓存数据
        self.db_manager.get_stage_output.side_effect = [
            None,  # story_flow缓存
            {"story_outline": [{"narrative_unit_number": 1}, {"narrative_unit_number": 2}]},  # story_outline
            {"global_narration_tone": "test"},  # script_strategy
        ]

        # Mock AI返回有效结果
        mock_ai_result = StoryFlowResponse(
            story_flow=[
                StoryFlowItem(narrative_unit_number=1, reasoning="开场"),
                StoryFlowItem(narrative_unit_number=2, reasoning="发展"),
            ]
        )

        self.stage.flow_client = Mock()
        self.stage.flow_client.call_ai_with_tool.return_value = mock_ai_result

        # Mock前置条件检查
        self.db_manager.get_stage_status.return_value = {"status": "completed"}

        # 执行测试
        result = self.stage.execute()

        # 验证成功执行
        assert result is True
        assert self.stage.flow_client.call_ai_with_tool.called
        assert self.db_manager.save_stage_output.called

    @patch("stages.stage_d2s_rewriter_flow.STORY_FLOW_ARRANGEMENT_PROMPT")
    def test_execute_ai_generation_with_duplicate_numbers(self, mock_prompt):
        """测试AI生成包含重复编号时的处理"""
        # Mock没有缓存数据
        self.db_manager.get_stage_output.side_effect = [
            None,  # story_flow缓存
            {"story_outline": [{"narrative_unit_number": 1}, {"narrative_unit_number": 2}]},  # story_outline
            {"global_narration_tone": "test"},  # script_strategy
        ]

        # Mock AI返回包含重复编号的结果
        mock_ai_result = StoryFlowResponse(
            story_flow=[
                StoryFlowItem(narrative_unit_number=1, reasoning="开场"),
                StoryFlowItem(narrative_unit_number=1, reasoning="重复"),  # 重复编号
            ]
        )

        self.stage.flow_client = Mock()
        self.stage.flow_client.call_ai_with_tool.return_value = mock_ai_result

        # Mock前置条件检查
        self.db_manager.get_stage_status.return_value = {"status": "completed"}

        # 执行测试
        result = self.stage.execute()

        # 验证检测到重复编号并返回失败
        assert result is False
        assert self.stage.logger.error.called


if __name__ == "__main__":
    test = TestStage17StoryFlow()
    test.setup_method()

    # 运行所有测试
    test_methods = [method for method in dir(test) if method.startswith("test_")]

    passed = 0
    failed = 0

    for method_name in test_methods:
        try:
            test.setup_method()
            getattr(test, method_name)()
            print(f"✓ {method_name}")
            passed += 1
        except Exception as e:
            print(f"✗ {method_name}: {e}")
            failed += 1

    print(f"\n测试结果: {passed} 通过, {failed} 失败")

    if failed == 0:
        print("所有测试通过！Stage17增强功能工作正常。")
        sys.exit(0)
    else:
        print("部分测试失败，请检查实现。")
        sys.exit(1)
