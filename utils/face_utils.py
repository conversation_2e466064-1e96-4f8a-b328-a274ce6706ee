"""
人脸识别工具模块
使用 facenet-pytorch 库提供人脸检测和嵌入提取功能。
"""

from pathlib import Path
from typing import Any, Dict, List

import cv2
import numpy as np
import torch
from facenet_pytorch import MTCNN, InceptionResnetV1
from PIL import Image

from config.settings import settings
from utils.logger import get_logger

logger = get_logger(__name__)


class FaceNetEmbedder:
    """
    封装 FaceNet (InceptionResnetV1) 模型，用于从视频帧中提取人脸嵌入。
    """

    def __init__(self):
        # 自动检测可用的设备 (优先CUDA, 其次MPS, 最后CPU)
        if torch.cuda.is_available():
            self.device = torch.device("cuda")
        elif torch.backends.mps.is_available():
            self.device = torch.device("mps")
        else:
            self.device = torch.device("cpu")

        logger.info(f"FaceNetEmbedder 正在使用设备: {self.device}")

        # 初始化主 MTCNN 实例，用于在目标设备上进行人脸提取（裁剪和标准化）
        self.mtcnn = MTCNN(
            keep_all=True,
            device=self.device,
            post_process=True,
            min_face_size=settings.MIN_FACE_SIZE,
            select_largest=False,
            image_size=160,
        )  # 明确指定 image_size

        # 为避免在 Apple Silicon (MPS) 设备上出现 "Adaptive pool MPS" 运行时错误，
        # 我们创建一个专门在 CPU 上运行的 MTCNN 实例，仅用于人脸检测步骤。
        # 这个错误是 PyTorch 在 MPS 后端上的一个已知问题。
        cpu_device = torch.device("cpu")
        logger.info("为避免MPS错误，创建了一个在CPU上运行的备用MTCNN检测器。")
        self.mtcnn_detect = MTCNN(
            keep_all=True,
            device=cpu_device,
            post_process=False,
            min_face_size=settings.MIN_FACE_SIZE,
            select_largest=False,
        )

        # 根据配置初始化 Inception ResNet V1 模型
        logger.info(f"正在加载 FaceNet (InceptionResnetV1) 模型，预训练权重: {settings.FACENET_PRETRAINED_MODEL}")
        self.resnet = InceptionResnetV1(pretrained=settings.FACENET_PRETRAINED_MODEL).eval().to(self.device)
        logger.info("FaceNet (InceptionResnetV1) 模型初始化完成。")

    def _get_embeddings_batch(self, face_tensors: torch.Tensor) -> np.ndarray:
        """对一批人脸张量进行嵌入提取"""
        with torch.no_grad():
            embeddings = self.resnet(face_tensors.to(self.device))
        return embeddings.cpu().numpy()

    def _process_image_for_embeddings(self, pil_image: Image.Image, frame_identifier: str = "") -> List[Dict[str, Any]]:
        """
        处理单张PIL图片，检测人脸并提取嵌入向量。
        这是从视频帧和静态图片中提取人脸的核心共享逻辑。
        """
        # --- 性能优化：将图像尺寸调整到 480p 高度 ---
        original_width, original_height = pil_image.size
        target_height = 480
        scale_factor = 1.0

        if original_height > target_height:
            scale_factor = original_height / target_height
            new_width = int(original_width / scale_factor)
            # 使用 LANCZOS 滤波器进行高质量缩放
            image_to_process = pil_image.resize((new_width, target_height), Image.Resampling.LANCZOS)
        else:
            image_to_process = pil_image

        # --- 【调试模式】准备工作 ---
        img_downscaled_cv = None
        img_original_cv = None
        debug_dir = None
        if settings.FACE_DEBUG_MODE and frame_identifier:
            debug_dir = settings.OUTPUT_DIR / "debug" / "faces"
            debug_dir.mkdir(parents=True, exist_ok=True)
            # 不保存被缩放用于检测的图像
            # image_to_process.save(debug_dir / f"{frame_identifier}_01_downscaled_for_detection.jpg")

            # 创建用于绘制的OpenCV图像副本
            img_downscaled_cv = cv2.cvtColor(np.array(image_to_process), cv2.COLOR_RGB2BGR)
            img_original_cv = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)

        # --- 步骤 1: 使用在CPU上运行的检测器获取边界框、置信度和关键点 ---
        boxes, probs, landmarks = self.mtcnn_detect.detect(image_to_process, landmarks=True)  # type: ignore

        if boxes is None:
            return []

        # --- 【调试模式】在缩放后的图像上绘制所有检测到的框 ---
        if (
            settings.FACE_DEBUG_MODE
            and frame_identifier
            and boxes is not None
            and img_downscaled_cv is not None
            and debug_dir is not None
        ):
            for i, box in enumerate(boxes):
                x1, y1, x2, y2 = [int(c) for c in box]
                color = (0, 255, 0) if probs[i] >= settings.FACENET_CONFIDENCE_THRESHOLD else (0, 0, 255)
                cv2.rectangle(img_downscaled_cv, (x1, y1), (x2, y2), color, 2)
                cv2.putText(
                    img_downscaled_cv,
                    f"{probs[i]:.2f}",
                    (x1, y1 - 10),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.5,
                    color,
                    1,
                )
            cv2.imwrite(str(debug_dir / f"{frame_identifier}_02_downscaled_with_boxes.jpg"), img_downscaled_cv)

        # --- 步骤 2: 过滤低置信度的人脸，并准备待处理的人脸 ---
        face_pils_to_process = []
        valid_boxes = []
        for i, box in enumerate(boxes):
            if probs[i] < settings.FACENET_CONFIDENCE_THRESHOLD:
                continue
            if landmarks[i] is None:
                logger.debug("检测到的区域因缺少面部关键点而被拒绝 (可能为误报)。")
                continue

            original_box = [coord * scale_factor for coord in box]
            x1, y1, x2, y2 = original_box
            face_width = x2 - x1
            face_height = y2 - y1
            if face_width < settings.MIN_FACE_SIZE or face_height < settings.MIN_FACE_SIZE:
                logger.debug(
                    f"检测到的人脸尺寸过小 ({int(face_width)}x{int(face_height)} < {settings.MIN_FACE_SIZE}px)，跳过。"
                )
                continue

            face_pil = pil_image.crop(original_box)
            face_for_blur_check = face_pil.resize((100, 100), Image.Resampling.LANCZOS)
            face_cv = cv2.cvtColor(np.array(face_for_blur_check), cv2.COLOR_RGB2BGR)
            gray_face = cv2.cvtColor(face_cv, cv2.COLOR_BGR2GRAY)
            if gray_face.size == 0:
                logger.debug("用于模糊检测的灰度图为空，跳过。")
                continue
            laplacian_var = cv2.Laplacian(gray_face, cv2.CV_64F).var()
            if laplacian_var < settings.BLUR_DETECTION_THRESHOLD:
                logger.debug(
                    f"检测到的人脸过于模糊 (标准化方差: {laplacian_var:.2f} < 阈值: {settings.BLUR_DETECTION_THRESHOLD})，跳过此人脸。"
                )
                continue

            face_pils_to_process.append(face_pil)
            valid_boxes.append(original_box)

        if not face_pils_to_process:
            return []

        # --- 【调试模式】【核心修复】在原始高分辨率图像上绘制最终被采纳的、经过缩放的框 ---
        if settings.FACE_DEBUG_MODE and frame_identifier and img_original_cv is not None and debug_dir is not None:
            for box in valid_boxes:
                x1, y1, x2, y2 = [int(c) for c in box]
                cv2.rectangle(img_original_cv, (x1, y1), (x2, y2), (0, 255, 0), 2)
            # 【核心修复】使用 frame_identifier 来确保文件名唯一
            output_path = debug_dir / f"{frame_identifier}_03_original_with_scaled_boxes.jpg"
            cv2.imwrite(str(output_path), img_original_cv)
            logger.debug(f"已保存调试图像到: {output_path}")

        # --- 步骤 3, 4, 5 (保持不变) ---
        face_tensors = []
        for face_pil in face_pils_to_process:
            resized_face = face_pil.resize((self.mtcnn.image_size, self.mtcnn.image_size), Image.Resampling.BILINEAR)
            np_face = np.array(resized_face, dtype=np.float32)
            tensor = torch.from_numpy(np_face.transpose((2, 0, 1)))
            std_tensor = (tensor - 127.5) / 128.0
            face_tensors.append(std_tensor)
        face_batch = torch.stack(face_tensors).to(self.device)
        embeddings = self._get_embeddings_batch(face_batch)
        processed_faces = []
        for i, embedding in enumerate(embeddings):
            x1, y1, x2, y2 = valid_boxes[i]
            processed_faces.append(
                {
                    "embedding": embedding,
                    "bounding_box": {
                        "x": int(x1),
                        "y": int(y1),
                        "w": int(x2 - x1),
                        "h": int(y2 - y1),
                    },
                }
            )
        return processed_faces

    def extract_embeddings_from_clip(self, clip_path: Path) -> List[Dict[str, Any]]:
        """
        从单个视频片段中提取所有人脸的嵌入向量。
        """
        results = []
        try:
            cap = cv2.VideoCapture(str(clip_path))
            if not cap.isOpened():
                logger.warning(f"无法打开视频文件进行人脸提取: {clip_path}")
                return []

            fps = cap.get(cv2.CAP_PROP_FPS)
            sampling_rate = settings.FACE_DETECTION_FRAME_SAMPLING_RATE
            frame_interval = max(1, int(fps / sampling_rate)) if fps > 0 else 1
            frame_count = 0

            while cap.isOpened():
                ret, frame = cap.read()
                if not ret:
                    break

                if frame_count % frame_interval == 0:
                    frame_pil = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
                    # 【核心修复】构建并传递 frame_identifier
                    frame_identifier = f"{clip_path.stem}_frame_{frame_count}"
                    processed_faces = self._process_image_for_embeddings(frame_pil, frame_identifier)
                    if processed_faces:
                        current_time_offset = frame_count / fps if fps > 0 else 0
                        for face_data in processed_faces:
                            face_data["time_offset"] = current_time_offset
                            results.append(face_data)
                frame_count += 1

            cap.release()
            logger.debug(f"在 {clip_path.name} 中提取到 {len(results)} 个原始人脸。")
            return results

        except Exception:
            logger.error(f"处理视频片段 {clip_path.name} 时发生未知错误。", exc_info=True)
            return []

    def extract_embeddings_from_image(self, image_path: Path) -> List[Dict[str, Any]]:
        """
        从单张静态图片中提取所有人脸的嵌入向量。
        """
        results = []
        try:
            img = Image.open(image_path).convert("RGB")
            # 【核心修复】构建并传递 frame_identifier
            frame_identifier = f"{image_path.stem}"
            results = self._process_image_for_embeddings(img, frame_identifier)
            logger.debug(f"在图片 {image_path.name} 中提取到 {len(results)} 个人脸嵌入。")
            return results
        except Exception:
            logger.error(f"处理图片 {image_path.name} 时发生未知错误。", exc_info=True)
            return []


_face_embedder_instance = None


def get_face_embedder() -> FaceNetEmbedder:
    """获取FaceNetEmbedder的单例。在首次调用时初始化。"""
    global _face_embedder_instance
    if _face_embedder_instance is None:
        _face_embedder_instance = FaceNetEmbedder()
    return _face_embedder_instance
