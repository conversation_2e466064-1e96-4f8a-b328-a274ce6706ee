"""
音频转写工具模块
使用 silero-vad 进行语音活动检测，然后使用 mlx-whisper 对检测到的语音片段进行转写。
"""

import tempfile
from pathlib import Path
from typing import Any, Dict, List, Optional, cast

import mlx_whisper
import torch
import torchaudio.transforms # 新增导入
import torchaudio # 保持原有导入

from config.settings import settings
from utils.logger import get_logger

logger = get_logger(__name__)


class MlxWhisperTranscriber:
    """
    封装 silero-vad 和 mlx-whisper 库，提供一个高效的音频转写流程。
    首先使用VAD检测语音片段，然后仅对这些片段进行转写。
    """

    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.model = settings.WHISPER_MODEL
        self.language = settings.WHISPER_LANGUAGE

        # VAD 相关配置
        self.vad_threshold = settings.VAD_THRESHOLD
        self.min_silence_duration_ms = settings.VAD_MIN_SILENCE_DURATION_MS
        self.min_speech_duration_ms = settings.VAD_MIN_SPEECH_DURATION_MS

        # 初始化 VAD 模型
        try:
            self.logger.info("正在加载 Silero VAD 模型...")
            # torch.hub.load for silero-vad 返回一个模型和一个包含多个工具函数的元组。
            # 我们需要解包这个元组以直接访问函数。
            vad_result = torch.hub.load(
                repo_or_dir="snakers4/silero-vad", model="silero_vad", force_reload=False, onnx=False
            )
            model, utils = cast(tuple, vad_result)
            self.vad_model = model
            # utils 是一个包含 (get_speech_timestamps, save_audio, ...) 的元组。
            # 我们通过索引直接获取所需的函数，以解决静态类型检查问题。
            self.get_speech_timestamps = utils[0]
            self.logger.info("Silero VAD 模型加载成功。")
        except Exception as e:
            self.logger.error(f"加载 Silero VAD 模型失败: {e}", exc_info=True)
            raise

        self.logger.info(f"MlxWhisperTranscriber 初始化完成。模型: {self.model}, 语言: {self.language}")

    def _transcribe_chunk(self, audio_chunk_path: Path) -> List[Dict[str, Any]]:
        """对单个音频片段进行转写"""
        try:
            result = mlx_whisper.transcribe(
                audio=str(audio_chunk_path),
                path_or_hf_repo=self.model,
                language=self.language,
                condition_on_previous_text=False,
                word_timestamps=False,
            )
            segments_data = result.get("segments", [])
            return cast(List[Dict[str, Any]], segments_data)
        except Exception as e:
            self.logger.error(f"转写音频块 {audio_chunk_path.name} 时出错: {e}", exc_info=True)
            return []

    def transcribe(self, audio_path: Path) -> List[Dict[str, Any]]:
        """
        执行完整的音频转写流程：
        1. 使用 Silero VAD 检测语音片段。
        2. 提取每个语音片段为临时音频文件。
        3. 对每个片段调用 mlx-whisper 进行转写。
        4. 合并所有转写结果，并校正时间戳。
        """
        if not audio_path.exists():
            self.logger.error(f"音频文件不存在: {audio_path}")
            return []

        self.logger.info(f"开始处理音频文件: {audio_path.name}")

        try:
            # 1. 使用 VAD 检测语音片段
            self.logger.info("正在使用 VAD 检测语音片段...")
            wav, original_sample_rate = torchaudio.load(audio_path)

            # Silero VAD 和 Whisper 都期望 16000 Hz 的采样率。
            # 如果原始采样率不同，则进行重采样。
            target_sample_rate = 16000
            if original_sample_rate != target_sample_rate:
                self.logger.info(
                    f"音频采样率为 {original_sample_rate} Hz，正在重采样至 {target_sample_rate} Hz..."
                )
                resampler = torchaudio.transforms.Resample(
                    orig_freq=original_sample_rate, new_freq=target_sample_rate
                )
                wav = resampler(wav)
                sample_rate = target_sample_rate
            else:
                sample_rate = original_sample_rate

            # Silero VAD 期望接收单声道音频张量。
            # 如果音频是立体声，我们通过平均声道将其转换为单声道。
            if wav.shape[0] > 1:
                self.logger.info(f"音频文件有 {wav.shape[0]} 个声道，正在转换为单声道...")
                wav = torch.mean(wav, dim=0, keepdim=True)

            speech_timestamps = self.get_speech_timestamps(  # 使用已解包的函数
                wav,
                self.vad_model,
                threshold=self.vad_threshold,
                min_silence_duration_ms=self.min_silence_duration_ms,
                min_speech_duration_ms=self.min_speech_duration_ms,
                sampling_rate=sample_rate,  # 使用重采样后的采样率
            )

            if not speech_timestamps:
                self.logger.warning("VAD 未在音频中检测到任何语音活动。")
                return []

            self.logger.info(f"VAD 检测到 {len(speech_timestamps)} 个语音片段。开始逐个转写...")

            all_transcripts = []
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                for i, ts in enumerate(speech_timestamps):
                    start_sample, end_sample = ts["start"], ts["end"]
                    chunk_start_time_sec = start_sample / sample_rate

                    # 提取音频块并保存到临时文件
                    chunk_wav = wav[:, start_sample:end_sample]
                    chunk_path = temp_path / f"chunk_{i}.mp3"
                    torchaudio.save(chunk_path, chunk_wav, sample_rate)

                    # --- 新增 DEBUG 日志 ---
                    self.logger.debug(
                        f"正在转写分块 {i+1}/{len(speech_timestamps)} (时间: {chunk_start_time_sec:.2f}s), "
                        f"临时文件: {chunk_path}"
                    )

                    # 对音频块进行转写
                    chunk_transcripts = self._transcribe_chunk(chunk_path)

                    # --- 新增 DEBUG 日志 ---
                    if chunk_transcripts:
                        self.logger.debug(f"分块 {i+1} 转写结果: {chunk_transcripts}")
                    else:
                        self.logger.debug(f"分块 {i+1} 未转写出任何内容。")

                    # 校正时间戳并添加到总列表
                    for transcript in chunk_transcripts:
                        transcript["start"] += chunk_start_time_sec
                        transcript["end"] += chunk_start_time_sec
                        all_transcripts.append(transcript)

            self.logger.info(f"音频转写完成，共合并生成 {len(all_transcripts)} 条带时间戳的文本。")
            return all_transcripts

        except Exception as e:
            self.logger.error(f"使用 VAD 和 mlx-whisper 进行转写时发生严重错误: {e}", exc_info=True)
            raise


_transcriber_instance: Optional[MlxWhisperTranscriber] = None


def get_transcriber() -> MlxWhisperTranscriber:
    """获取MlxWhisperTranscriber的单例。在首次调用时初始化。"""
    global _transcriber_instance
    if _transcriber_instance is None:
        _transcriber_instance = MlxWhisperTranscriber()
    return _transcriber_instance
