"""
图谱处理工具模块
提供图的遍历、环检测和破环等功能
"""

from typing import Any, Dict, List, Set, Tuple, Optional

from utils.logger import get_logger

logger = get_logger(__name__)


class GraphProcessor:
    """图谱处理器，用于处理因果图谱的逻辑"""

    def __init__(self, nodes: List[Dict[str, Any]], edges: List[Dict[str, Any]]):
        self.nodes = {node["event_id"]: node for node in nodes}
        self.adj: Dict[str, List[str]] = {node_id: [] for node_id in self.nodes}
        self.edges = edges
        for edge in self.edges:
            source = edge.get("source_event_id")
            target = edge.get("target_event_id")
            if source in self.adj and target in self.adj:
                self.adj[source].append(target)

    def _find_cycle(self) -> Optional[List[str]]:
        """
        使用深度优先搜索（DFS）查找图中的一个环。
        返回环的路径，如果无环则返回None。
        """
        path: Set[str] = set()
        visited: Set[str] = set()

        for node_id in self.nodes:
            if node_id not in visited:
                # 存储DFS路径上的节点
                recursion_stack: List[str] = []
                if self._find_cycle_util(node_id, visited, path, recursion_stack):
                    # 找到了环，返回环路径
                    try:
                        # 找到环的起始点在recursion_stack中的位置
                        cycle_start_index = recursion_stack.index(recursion_stack[-1])
                        return recursion_stack[cycle_start_index:]
                    except ValueError:
                        # 理论上不应发生，但作为保护
                        return recursion_stack
        return None

    def _find_cycle_util(self, u: str, visited: Set[str], path: Set[str], recursion_stack: List[str]) -> bool:
        """DFS的辅助函数"""
        visited.add(u)
        path.add(u)
        recursion_stack.append(u)

        for v in self.adj.get(u, []):
            if v not in visited:
                if self._find_cycle_util(v, visited, path, recursion_stack):
                    return True
            elif v in path:
                # 检测到环
                recursion_stack.append(v)
                return True

        path.remove(u)
        recursion_stack.pop()
        return False

    def break_cycles_greedy(self) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        贪婪地破除图中的所有环。
        它会重复查找环，并移除环中权重最低（此处简化为随机移除）的边，直到图中无环。
        返回被移除的边和精炼后的边。
        """
        refined_edges = self.edges[:]
        removed_edges = []

        while True:
            # 基于当前的 refined_edges 重新构建邻接表并查找环
            self.adj = {node_id: [] for node_id in self.nodes}
            for edge in refined_edges:
                source = edge.get("source_event_id")
                target = edge.get("target_event_id")
                if source in self.adj and target in self.adj:
                    self.adj[source].append(target)

            cycle = self._find_cycle()
            if not cycle:
                logger.info("图中未发现逻辑循环，无需破环。")
                break

            logger.warning(f"检测到逻辑循环: {' -> '.join(cycle)}")

            # 贪婪策略：移除环中的一条边。
            # 简单起见，我们移除构成环的最后一条边。
            # 在一个更复杂的实现中，可以移除权重最低的边。
            u, v = cycle[-2], cycle[-1]

            edge_to_remove = None
            for i, edge in enumerate(refined_edges):
                if edge.get("source_event_id") == u and edge.get("target_event_id") == v:
                    edge_to_remove = refined_edges.pop(i)
                    break

            if edge_to_remove:
                removed_edges.append(edge_to_remove)
                logger.info(f"已移除边 '{u} -> {v}' 以破除循环。")
            else:
                # 如果找不到边，说明图结构有问题，强制跳出循环以避免死循环
                logger.error(f"无法找到并移除边 '{u} -> {v}'，破环过程异常终止。")
                break

        return removed_edges, refined_edges


# 全局图谱处理器实例 (如果需要)
# graph_processor = GraphProcessor(...)
