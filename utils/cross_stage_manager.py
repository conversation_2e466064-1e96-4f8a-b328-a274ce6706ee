"""
跨阶段信息管理器
统一管理各阶段间的信息共享和一致性检查
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import numpy as np

from utils.logger import setup_logger


@dataclass
class ConsistencyViolation:
    """一致性违规记录"""

    type: str
    stage_number: int
    character_id: Optional[str] = None
    expected: Any = None
    actual: Any = None
    severity: str = "medium"  # low, medium, high
    description: str = ""


class CrossStageInformationManager:
    """跨阶段信息管理器"""

    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.logger = setup_logger(__name__)
        self.shared_cache = {}
        self.consistency_violations = []

    def register_stage_output(self, video_id: int, stage_number: int, output_data: Dict, metadata: Optional[Dict] = None):
        """注册阶段输出并更新共享信息"""
        try:
            # 提取关键信息到共享缓存
            if stage_number == 6:  # 角色识别
                self._extract_character_info(video_id, output_data)
            elif stage_number == 7:  # 场景聚合
                self._extract_scene_structure(video_id, output_data)
            elif stage_number == 11:  # 事件识别
                self._extract_narrative_events(video_id, output_data)
            elif stage_number == 15:  # 角色档案
                self._extract_character_dossiers(video_id, output_data)

            # 执行一致性检查
            violations = self._validate_consistency(video_id, stage_number, output_data)
            if violations:
                self.consistency_violations.extend(violations)
                self._log_violations(video_id, stage_number, violations)

        except Exception as e:
            self.logger.error(f"注册阶段{stage_number}输出时出错: {e}")

    def get_character_context(self, video_id: int) -> Dict:
        """获取角色上下文信息"""
        cache_key = f"characters_{video_id}"
        return self.shared_cache.get(cache_key, {})

    def get_narrative_context(self, video_id: int) -> Dict:
        """获取叙事上下文信息"""
        cache_key = f"narrative_{video_id}"
        return self.shared_cache.get(cache_key, {})

    def _extract_character_info(self, video_id: int, character_data: Dict):
        """提取角色信息到共享缓存"""
        cache_key = f"characters_{video_id}"

        characters = character_data.get("characters", [])
        character_profiles = {}

        for char in characters:
            char_id = char.get("character_id")
            if char_id:
                character_profiles[char_id] = {
                    "name": char.get("name"),
                    "description": char.get("description"),
                    "visual_description": char.get("visual_description"),
                    "personality_traits": char.get("personality_traits", []),
                    "relationships": char.get("relationships", []),
                }

        self.shared_cache[cache_key] = {
            "character_profiles": character_profiles,
            "character_count": len(characters),
            "last_updated_stage": 6,
        }

        self.logger.info(f"提取了{len(characters)}个角色信息到共享缓存")

    def _extract_scene_structure(self, video_id: int, scene_data: Dict):
        """提取场景结构信息"""
        # 从数据库获取场景信息
        scenes = self.db_manager.get_all_scenes_with_characters(video_id)

        cache_key = f"scenes_{video_id}"
        self.shared_cache[cache_key] = {
            "scene_count": len(scenes),
            "total_duration": sum(s.get("end_time", 0) - s.get("start_time", 0) for s in scenes),
            "character_scene_matrix": self._build_character_scene_matrix(scenes),
            "last_updated_stage": 7,
        }

    def _extract_narrative_events(self, video_id: int, events_data: Dict):
        """提取叙事事件信息"""
        cache_key = f"narrative_{video_id}"

        events = events_data.get("narrative_events", [])

        # 统计角色在事件中的重要性
        character_importance = {}
        for event in events:
            importance = event.get("importance_score", 0.5)
            for char in event.get("characters_present", []):
                if char not in character_importance:
                    character_importance[char] = {"total_importance": 0, "event_count": 0}
                character_importance[char]["total_importance"] += importance
                character_importance[char]["event_count"] += 1

        self.shared_cache[cache_key] = {
            "event_count": len(events),
            "character_importance_scores": character_importance,
            "avg_importance_score": np.mean([e.get("importance_score", 0.5) for e in events]),
            "last_updated_stage": 11,
        }

    def _extract_character_dossiers(self, video_id: int, dossier_data: Dict):
        """提取角色档案信息"""
        cache_key = f"characters_{video_id}"
        existing_data = self.shared_cache.get(cache_key, {})

        dossiers = dossier_data.get("dossiers", [])
        dossier_dict = {d.get("character_id"): d for d in dossiers}

        existing_data["character_dossiers"] = dossier_dict
        existing_data["last_updated_stage"] = 15

        self.shared_cache[cache_key] = existing_data

    def _build_character_scene_matrix(self, scenes: List[Dict]) -> Dict:
        """构建角色-场景矩阵"""
        matrix = {}
        for scene in scenes:
            scene_id = scene.get("id")
            characters = scene.get("character_names", [])

            for char in characters:
                if char not in matrix:
                    matrix[char] = []
                matrix[char].append(scene_id)

        return matrix

    def _validate_consistency(self, video_id: int, stage_number: int, output_data: Dict) -> List[ConsistencyViolation]:
        """验证一致性"""
        violations = []

        if stage_number >= 11:  # 从叙事阶段开始检查
            # 角色名称一致性检查
            char_violations = self._check_character_name_consistency(video_id, stage_number, output_data)
            violations.extend(char_violations)

            # 角色出现合理性检查
            presence_violations = self._check_character_presence_consistency(video_id, stage_number, output_data)
            violations.extend(presence_violations)

        return violations

    def _check_character_name_consistency(
        self, video_id: int, stage_number: int, output_data: Dict
    ) -> List[ConsistencyViolation]:
        """检查角色名称一致性"""
        violations = []

        # 获取基准角色信息
        char_cache = self.get_character_context(video_id)
        baseline_profiles = char_cache.get("character_profiles", {})

        if not baseline_profiles:
            return violations

        # 从当前输出中提取角色名称
        current_characters = self._extract_characters_from_output(stage_number, output_data)

        for char_id, current_info in current_characters.items():
            if char_id in baseline_profiles:
                baseline_name = baseline_profiles[char_id].get("name")
                current_name = current_info.get("name")

                if baseline_name and current_name and baseline_name != current_name:
                    violations.append(
                        ConsistencyViolation(
                            type="character_name_mismatch",
                            stage_number=stage_number,
                            character_id=char_id,
                            expected=baseline_name,
                            actual=current_name,
                            severity="high",
                            description=f"角色{char_id}名称不一致",
                        )
                    )

        return violations

    def _check_character_presence_consistency(
        self, video_id: int, stage_number: int, output_data: Dict
    ) -> List[ConsistencyViolation]:
        """检查角色出现的合理性"""
        violations = []

        # 获取角色-场景矩阵
        scene_cache = self.shared_cache.get(f"scenes_{video_id}", {})
        char_scene_matrix = scene_cache.get("character_scene_matrix", {})

        if not char_scene_matrix:
            return violations

        # 从当前输出中提取角色出现信息
        if stage_number == 11:  # 事件识别
            events = output_data.get("narrative_events", [])
            for event in events:
                event_chars = event.get("characters_present", [])
                source_scenes = event.get("source_scene_numbers", [])

                for char in event_chars:
                    if char in char_scene_matrix:
                        char_scenes = set(char_scene_matrix[char])
                        event_scene_set = set(source_scenes)

                        # 检查角色是否在相应场景中出现
                        if not char_scenes.intersection(event_scene_set):
                            violations.append(
                                ConsistencyViolation(
                                    type="character_presence_inconsistency",
                                    stage_number=stage_number,
                                    character_id=char,
                                    severity="medium",
                                    description=f"角色{char}在事件中出现但在对应场景中未发现",
                                )
                            )

        return violations

    def _extract_characters_from_output(self, stage_number: int, output_data: Dict) -> Dict[str, Dict]:
        """从输出数据中提取角色信息"""
        characters = {}

        if stage_number == 11:  # 事件识别
            events = output_data.get("narrative_events", [])
            for event in events:
                for char_name in event.get("characters_present", []):
                    characters[char_name] = {"name": char_name}

        elif stage_number == 15:  # 角色档案
            dossiers = output_data.get("dossiers", [])
            for dossier in dossiers:
                char_id = dossier.get("character_id")
                if char_id:
                    characters[char_id] = dossier

        return characters

    def _log_violations(self, video_id: int, stage_number: int, violations: List[ConsistencyViolation]):
        """记录一致性违规"""
        if not violations:
            return

        high_severity = [v for v in violations if v.severity == "high"]
        medium_severity = [v for v in violations if v.severity == "medium"]

        if high_severity:
            self.logger.warning(f"阶段{stage_number}发现{len(high_severity)}个高严重性一致性问题")
            for violation in high_severity:
                self.logger.warning(f"  - {violation.description}")

        if medium_severity:
            self.logger.info(f"阶段{stage_number}发现{len(medium_severity)}个中等严重性一致性问题")

    def get_consistency_report(self, video_id: int) -> Dict:
        """获取一致性报告"""
        video_violations = [v for v in self.consistency_violations if v.character_id or True]  # 过滤该视频的违规记录

        severity_counts = {
            "high": len([v for v in video_violations if v.severity == "high"]),
            "medium": len([v for v in video_violations if v.severity == "medium"]),
            "low": len([v for v in video_violations if v.severity == "low"]),
        }

        return {
            "total_violations": len(video_violations),
            "severity_breakdown": severity_counts,
            "violations": [
                {
                    "type": v.type,
                    "stage": v.stage_number,
                    "character": v.character_id,
                    "severity": v.severity,
                    "description": v.description,
                }
                for v in video_violations
            ],
            "consistency_score": self._calculate_consistency_score(video_violations),
        }

    def _calculate_consistency_score(self, violations: List[ConsistencyViolation]) -> float:
        """计算一致性分数"""
        if not violations:
            return 1.0

        # 根据严重性计算扣分
        penalty = 0.0
        for violation in violations:
            if violation.severity == "high":
                penalty += 0.3
            elif violation.severity == "medium":
                penalty += 0.15
            else:  # low
                penalty += 0.05

        return max(0.0, 1.0 - penalty)
