"""
增强的基础Stage类，支持跨阶段信息融合和质量控制
"""

from typing import Dict, Optional, List, Any, TYPE_CHECKING
import time

from utils.cross_stage_manager import CrossStageInformationManager
from utils.logger import setup_logger

if TYPE_CHECKING:
    from database.models import DatabaseManager
    import logging


class QualityControlMixin:
    """质量控制混入类"""

    def get_quality_threshold(self) -> float:
        """获取质量阈值，子类可重写"""
        quality_thresholds = {
            6: 0.8,  # 角色识别要求高准确度
            7: 0.7,  # 场景聚合
            11: 0.75,  # 事件识别
            12: 0.7,  # 因果推理
            15: 0.8,  # 角色档案
            16: 0.75,  # 剧本策略要求高创意性
            18: 0.8,  # 剧本生成要求高质量
            19: 0.85,  # 精炼阶段要求最高质量
        }
        # 通过self获取stage_number（假设继承类有此属性）
        stage_num = getattr(self, "stage_number", 0)
        return quality_thresholds.get(stage_num, 0.7)

    def evaluate_stage_quality(self, output_data: Dict[str, Any]) -> float:
        """评估阶段输出质量，子类可重写"""
        # 基础质量检查
        if not output_data:
            return 0.0

        # 获取阶段编号
        stage_num = getattr(self, "stage_number", 0)

        # 根据阶段类型进行基础质量评估
        if stage_num == 11:  # 事件识别
            return self._evaluate_event_identification_quality(output_data)
        elif stage_num == 12:  # 因果推理
            return self._evaluate_causal_reasoning_quality(output_data)
        elif stage_num == 15:  # 角色档案
            return self._evaluate_character_dossier_quality(output_data)
        elif stage_num == 16:  # 剧本策略
            return self._evaluate_script_strategy_quality(output_data)

        return 0.8  # 默认质量分数

    def _evaluate_event_identification_quality(self, output_data: Dict[str, Any]) -> float:
        """评估事件识别质量"""
        events = output_data.get("narrative_events", [])
        if not events:
            return 0.0

        score = 0.0

        # 1. 数量合理性检查
        event_count = len(events)
        if 3 <= event_count <= 15:  # 合理的事件数量范围
            score += 0.3
        else:
            score += 0.1

        # 2. 重要性分布检查
        importance_scores = [e.get("importance_score", 0.5) for e in events]
        avg_importance = sum(importance_scores) / len(importance_scores)
        if avg_importance >= 0.6:  # 平均重要性足够高
            score += 0.3
        else:
            score += 0.1

        # 3. 信息完整性检查
        complete_events = 0
        for event in events:
            if event.get("event_description") and event.get("characters_present") and event.get("source_scene_numbers"):
                complete_events += 1

        completeness_ratio = complete_events / len(events)
        score += completeness_ratio * 0.4

        return min(1.0, score)

    def _evaluate_causal_reasoning_quality(self, output_data: Dict[str, Any]) -> float:
        """评估因果推理质量"""
        nodes = output_data.get("nodes", [])
        edges = output_data.get("edges", [])

        if not nodes or not edges:
            return 0.0

        score = 0.0

        # 1. 连接密度检查
        connection_ratio = len(edges) / len(nodes) if nodes else 0
        if 0.3 <= connection_ratio <= 2.0:  # 合理的连接密度
            score += 0.4
        else:
            score += 0.2

        # 2. 因果链接质量检查
        quality_links = 0
        for edge in edges:
            if edge.get("causality_description") and len(edge.get("causality_description", "")) > 10:
                quality_links += 1

        link_quality_ratio = quality_links / len(edges) if edges else 0
        score += link_quality_ratio * 0.6

        return min(1.0, score)

    def _evaluate_character_dossier_quality(self, output_data: Dict[str, Any]) -> float:
        """评估角色档案质量"""
        dossiers = output_data.get("dossiers", [])
        if not dossiers:
            return 0.0

        score = 0.0
        complete_dossiers = 0

        for dossier in dossiers:
            required_fields = ["character_id", "name", "background", "motivation", "arc"]
            if all(dossier.get(field) for field in required_fields):
                complete_dossiers += 1

        completeness_ratio = complete_dossiers / len(dossiers)
        score = completeness_ratio

        return score

    def _evaluate_script_strategy_quality(self, output_data: Dict[str, Any]) -> float:
        """评估剧本策略质量"""
        if not output_data:
            return 0.0

        score = 0.0
        
        # 1. 检查必需字段的存在性和质量
        required_fields = ["global_narration_tone", "hook_strategy", "climax_strategy", "conclusion_strategy"]
        field_scores = []
        
        for field in required_fields:
            field_value = output_data.get(field, "")
            if field_value and len(field_value) > 20:  # 基本长度要求
                field_score = self._evaluate_strategy_content_quality(field_value)
                field_scores.append(field_score)
            else:
                field_scores.append(0.0)
        
        # 字段完整性分数（占40%）
        completeness_score = sum(1 for s in field_scores if s > 0) / len(required_fields)
        score += completeness_score * 0.4
        
        # 内容质量分数（占60%）
        if field_scores:
            avg_content_quality = sum(field_scores) / len(field_scores)
            score += avg_content_quality * 0.6
        
        return min(1.0, score)
    
    def _evaluate_strategy_content_quality(self, content: str) -> float:
        """评估单个策略内容的质量"""
        if not content:
            return 0.0
            
        quality_score = 0.0
        
        # 1. 长度适中性 (20%)
        length = len(content)
        if 50 <= length <= 500:  # 理想长度范围
            quality_score += 0.2
        elif length > 20:  # 至少有基本内容
            quality_score += 0.1
            
        # 2. 避免陈词滥调 (30%)
        cliches = ["棋局", "牢笼", "自由", "命运", "掌握", "打破", "钥匙", "谜团"]
        cliche_count = sum(1 for cliche in cliches if cliche in content)
        if cliche_count <= 2:  # 少量使用可接受
            quality_score += 0.3
        elif cliche_count <= 4:
            quality_score += 0.15
            
        # 3. 具体性和细节 (30%)
        detail_indicators = ["镜头", "画面", "声音", "动作", "表情", "环境", "道具", "色彩"]
        detail_count = sum(1 for indicator in detail_indicators if indicator in content)
        if detail_count >= 3:
            quality_score += 0.3
        elif detail_count >= 1:
            quality_score += 0.15
            
        # 4. 情感深度 (20%)
        emotion_indicators = ["恐惧", "希望", "愤怒", "悲伤", "欣慰", "紧张", "温暖", "孤独", "坚定"]
        emotion_count = sum(1 for emotion in emotion_indicators if emotion in content)
        if emotion_count >= 2:
            quality_score += 0.2
        elif emotion_count >= 1:
            quality_score += 0.1
            
        return quality_score


class EnhancedBaseStage(QualityControlMixin):
    """增强的基础阶段混入类，支持多模态信息融合和质量控制

    这是一个混入类，应该与BaseStage一起使用：
    class MyStage(BaseStage, EnhancedBaseStage):
        ...
    """

    # 声明预期从BaseStage继承的属性
    db_manager: "DatabaseManager"
    video_id: int
    logger: "logging.Logger"

    # 声明预期从BaseStage继承的属性（这些会作为@property实现）
    @property
    def stage_number(self) -> int:
        """阶段编号 - 应该由子类实现"""
        ...

    @property
    def stage_name(self) -> str:
        """阶段名称 - 应该由子类实现"""
        ...

    # 声明预期从BaseStage继承的方法
    def update_progress(self, progress_info: str) -> None:
        """更新进度信息 - 这个方法应该由BaseStage提供"""
        ...

    def __init__(self, db_manager: "DatabaseManager", video_id: int, *args: Any, **kwargs: Any) -> None:
        # 不调用super().__init__，因为作为mixin，这会干扰多重继承
        # 只负责初始化自己的属性
        self.cross_stage_manager = CrossStageInformationManager(db_manager)
        self.quality_logger = setup_logger(f"{self.__class__.__name__}_Quality")
        self.max_retries = 2  # 最大重试次数

    def execute_with_enhancement(self, force_level: Optional[str] = None, **kwargs: Any) -> bool:
        """带增强功能的执行方法"""
        start_time = time.time()

        try:
            # 获取跨阶段上下文信息
            cross_stage_context = self._get_cross_stage_context()

            # 执行阶段逻辑
            result = self._execute_enhanced(cross_stage_context, force_level, **kwargs)

            if result:
                # 获取输出数据
                output_data = self.get_stage_output()

                # 质量评估
                quality_score = self.evaluate_stage_quality(output_data)
                execution_time = time.time() - start_time
                stage_num = getattr(self, "stage_number", 0)

                self.quality_logger.info(
                    f"阶段{stage_num}执行完成 - 质量分数: {quality_score:.3f}, 用时: {execution_time:.2f}s"
                )

                # 质量检查和重试逻辑
                if quality_score < self.get_quality_threshold():
                    self.quality_logger.warning(
                        f"阶段{stage_num}质量分数{quality_score:.3f}低于阈值{self.get_quality_threshold()}"
                    )

                    # 尝试重试
                    retry_result = self._attempt_retry_with_enhancement(
                        cross_stage_context, quality_score, force_level, **kwargs
                    )

                    if not retry_result:
                        self.quality_logger.error(f"阶段{stage_num}重试后仍未达到质量要求")
                        # 可以选择继续或失败，这里选择继续但记录警告
                        result = True

                # 注册输出到跨阶段管理器
                if result:
                    self.cross_stage_manager.register_stage_output(self.video_id, stage_num, output_data)

            return result

        except Exception as e:
            stage_num = getattr(self, "stage_number", 0)
            self.logger.error(f"阶段{stage_num}增强执行失败: {e}", exc_info=True)
            return False

    def _get_cross_stage_context(self) -> Dict[str, Any]:
        """获取跨阶段上下文信息"""
        context = {}
        stage_num = getattr(self, "stage_number", 0)

        # 获取角色上下文
        if stage_num >= 11:
            character_context = self.cross_stage_manager.get_character_context(self.video_id)
            if character_context:
                context["character_context"] = character_context

        # 获取叙事上下文
        if stage_num >= 12:
            narrative_context = self.cross_stage_manager.get_narrative_context(self.video_id)
            if narrative_context:
                context["narrative_context"] = narrative_context

        return context

    def _execute_enhanced(
        self, cross_stage_context: Dict[str, Any], force_level: Optional[str] = None, **kwargs: Any
    ) -> bool:
        """
        带上下文的增强执行方法，子类应该重写此方法以支持跨阶段上下文。
        这是一个抽象方法的替代，子类必须实现。
        """
        raise NotImplementedError("子类必须实现 _execute_enhanced 方法")

    def _attempt_retry_with_enhancement(
        self,
        cross_stage_context: Dict[str, Any],
        initial_quality_score: float,
        force_level: Optional[str] = None,
        **kwargs: Any,
    ) -> bool:
        """尝试带增强的重试"""
        stage_num = getattr(self, "stage_number", 0)

        for retry_attempt in range(self.max_retries):
            self.quality_logger.info(f"阶段{stage_num}开始第{retry_attempt + 1}次重试")

            # 增强上下文信息用于重试
            enhanced_context = self._enhance_context_for_retry(
                cross_stage_context, retry_attempt, initial_quality_score
            )

            try:
                # 清理之前的输出
                output_types = {11: "narrative_events", 12: "causal_graph", 15: "character_dossier", 16: "script_strategy"}
                output_type_to_clear = output_types.get(stage_num)

                if output_type_to_clear:
                    self.db_manager.clear_stage_output(self.video_id, stage_num, output_type_to_clear)
                else:
                    self.logger.warning(
                        f"阶段 {stage_num} 没有在 enhanced_base_stage 中定义 output_type，无法自动清理。"
                    )

                # 重新执行
                result = self._execute_enhanced(enhanced_context, force_level, **kwargs)

                if result:
                    # 重新评估质量
                    output_data = self.get_stage_output()
                    retry_quality_score = self.evaluate_stage_quality(output_data)

                    self.quality_logger.info(f"重试后质量分数: {retry_quality_score:.3f}")

                    if retry_quality_score >= self.get_quality_threshold():
                        self.quality_logger.info(f"阶段{stage_num}重试成功")
                        return True

            except Exception as e:
                self.quality_logger.error(f"重试第{retry_attempt + 1}次失败: {e}")

        return False

    def _enhance_context_for_retry(
        self, base_context: Dict[str, Any], retry_attempt: int, initial_quality_score: float
    ) -> Dict[str, Any]:
        """为重试增强上下文"""
        enhanced_context = base_context.copy()

        # 添加重试指导信息
        retry_guidance = {
            "retry_attempt": retry_attempt + 1,
            "initial_quality_score": initial_quality_score,
            "quality_threshold": self.get_quality_threshold(),
            "improvement_suggestions": self._generate_improvement_suggestions(initial_quality_score, retry_attempt),
        }

        enhanced_context["retry_guidance"] = retry_guidance

        return enhanced_context

    def _generate_improvement_suggestions(self, quality_score: float, retry_attempt: int) -> List[str]:
        """生成改进建议"""
        suggestions = []
        stage_num = getattr(self, "stage_number", 0)

        if stage_num == 11:  # 事件识别
            if quality_score < 0.5:
                suggestions.append("增加事件重要性评分的准确性")
                suggestions.append("确保事件描述足够详细")
            suggestions.append("检查角色在事件中的出现是否合理")

        elif stage_num == 12:  # 因果推理
            suggestions.append("深入分析角色的心理动机")
            suggestions.append("建立更完整的因果链条")
            if retry_attempt > 0:
                suggestions.append("简化因果关系分析，关注核心链条")

        elif stage_num == 15:  # 角色档案
            suggestions.append("确保所有必填字段都有内容")
            suggestions.append("丰富角色背景和动机描述")
        
        elif stage_num == 16:  # 剧本策略
            if quality_score < 0.5:
                suggestions.append("避免使用陈词滥调，寻找更独特的表达方式")
                suggestions.append("增加具体的视觉和听觉细节描述")
            suggestions.append("深化情感层次，超越表面冲突")
            suggestions.append("结合视频素材的独特特点，避免泛化描述")

        return suggestions

    def get_stage_output(self) -> Dict[str, Any]:
        """获取阶段输出数据，子类可重写"""
        # 从数据库获取最新的输出数据
        output_types = {11: "narrative_events", 12: "causal_graph", 15: "character_dossier", 16: "script_strategy"}

        stage_num = getattr(self, "stage_number", 0)
        output_type = output_types.get(stage_num)
        if output_type:
            return self.db_manager.get_stage_output(self.video_id, stage_num, output_type) or {}

        return {}

    def get_consistency_report(self) -> Dict[str, Any]:
        """获取一致性报告"""
        return self.cross_stage_manager.get_consistency_report(self.video_id)
