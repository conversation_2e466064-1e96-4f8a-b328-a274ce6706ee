"""
视频处理工具模块
提供视频分析、场景检测等功能
"""

import hashlib
import subprocess
import time
import xml.etree.ElementTree as ET
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
from xml.dom import minidom

import cv2
import numpy as np

from utils.logger import get_logger
from utils.shared_state import shutdown_event

logger = get_logger(__name__)


class VideoProcessor:
    """视频处理器"""

    def __init__(self):
        pass

    def _add_keyframed_audio_level_filter(
        self,
        parent_clipitem: ET.Element,
        ducking_info: Dict[str, float],
        clip_timeline_start: float,
        timebase: int,
        duck_db: int,
    ):
        """为音频剪辑添加带关键帧的音量级别过滤器，以实现动态音频闪避。"""
        filter_el = ET.SubElement(parent_clipitem, "filter")
        effect = ET.SubElement(filter_el, "effect")
        ET.SubElement(effect, "name").text = "Audio Levels"
        ET.SubElement(effect, "effectid").text = "audiolevels"
        # ... (effect 的其他属性设置与 _add_audio_level_filter 相同)
        ET.SubElement(effect, "effectcategory").text = "audiolevels"
        ET.SubElement(effect, "effecttype").text = "filter"
        ET.SubElement(effect, "mediatype").text = "audio"

        param = ET.SubElement(effect, "parameter")
        ET.SubElement(param, "parameterid").text = "level"
        ET.SubElement(param, "name").text = "Level"
        # 保证 FCP 正确解析范围，避免将 0 视为 -∞
        ET.SubElement(param, "valuemin").text = "-999"
        ET.SubElement(param, "valuemax").text = "12"
        ET.SubElement(param, "valuemin").text = "-999"
        ET.SubElement(param, "valuemax").text = "12"

        # --- 核心：创建关键帧 ---
        # 计算关键帧在剪辑片段内部的时间点（以帧为单位）
        duck_start_in_clip_frames = int((ducking_info["start"] - clip_timeline_start) * timebase)
        duck_end_in_clip_frames = int((ducking_info["end"] - clip_timeline_start) * timebase)
        
        # 确保关键帧不会超出片段范围
        clip_duration_frames = int(parent_clipitem.findtext("duration", "0"))
        duck_start_in_clip_frames = max(0, duck_start_in_clip_frames)
        duck_end_in_clip_frames = min(clip_duration_frames, duck_end_in_clip_frames)

        # 定义关键帧列表
        keyframes = [
            (duck_start_in_clip_frames, 0),          # Keyframe 1: 闪避开始前，音量正常 (0 dB)
            (duck_start_in_clip_frames + 1, duck_db),# Keyframe 2: 闪避开始，音量降低
            (duck_end_in_clip_frames, duck_db),      # Keyframe 3: 闪避结束前，音量保持降低
            (duck_end_in_clip_frames + 1, 0),        # Keyframe 4: 闪避结束，音量恢复正常
        ]

        for frame, value in keyframes:
            if 0 <= frame <= clip_duration_frames: # 仅在有效范围内添加关键帧
                keyframe = ET.SubElement(param, "keyframe")
                ET.SubElement(keyframe, "when").text = str(frame)
                ET.SubElement(keyframe, "value").text = str(value)

    def _add_audio_level_filter(self, parent_clipitem: ET.Element, level_db: int):
        """为音频剪辑添加音量级别过滤器。"""
        filter_el = ET.SubElement(parent_clipitem, "filter")
        effect = ET.SubElement(filter_el, "effect")
        ET.SubElement(effect, "name").text = "Audio Levels"
        ET.SubElement(effect, "effectid").text = "audiolevels"
        ET.SubElement(effect, "effectcategory").text = "audiolevels"
        ET.SubElement(effect, "effecttype").text = "filter"
        ET.SubElement(effect, "mediatype").text = "audio"
        param = ET.SubElement(effect, "parameter")
        ET.SubElement(param, "parameterid").text = "level"
        ET.SubElement(param, "name").text = "Level"
        value_el = ET.SubElement(param, "value")
        value_el.text = str(level_db)

    def calculate_file_hash(self, video_path: Path) -> str:
        """计算文件的SHA256哈希值，用于唯一标识"""
        sha256 = hashlib.sha256()
        try:
            with open(video_path, "rb") as f:
                while chunk := f.read(8192):  # 读取8KB的块
                    sha256.update(chunk)
            file_hash = sha256.hexdigest()
            logger.info(f"计算文件 '{video_path.name}' 的哈希值为: {file_hash[:12]}...")
            return file_hash
        except Exception as e:
            logger.error(f"计算文件哈希值时出错: {e}")
            raise

    def _get_fps_with_ffprobe(self, video_path: Path) -> Optional[float]:
        """使用 ffprobe 作为备用方法获取视频的FPS，更可靠。"""
        try:
            cmd = [
                "ffprobe",
                "-v",
                "error",
                "-select_streams",
                "v:0",
                "-show_entries",
                "stream=r_frame_rate",
                "-of",
                "default=noprint_wrappers=1:nokey=1",
                str(video_path),
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            frame_rate_str = result.stdout.strip()

            # ffprobe 的输出通常是分数形式，如 "24000/1001"
            if "/" in frame_rate_str:
                num, den = frame_rate_str.split("/")
                return float(num) / float(den)
            else:
                return float(frame_rate_str)
        except FileNotFoundError:
            logger.warning("ffprobe 命令未找到。无法使用备用方法获取FPS。请确保已安装ffmpeg。")
            return None
        except Exception as e:
            logger.warning(f"使用 ffprobe 获取FPS失败: {e}")
            return None

    def get_video_info(self, video_path: Path) -> dict:
        """获取视频基本信息，并在OpenCV失败时使用ffprobe作为备用方案。"""
        try:
            cap = cv2.VideoCapture(str(video_path))
            if not cap.isOpened():
                raise ValueError(f"无法打开视频文件: {video_path}")

            # 1. 尝试使用 OpenCV 获取 fps
            fps = cap.get(cv2.CAP_PROP_FPS)

            # 2. 如果 OpenCV 返回无效值，则使用 ffprobe 作为备用
            if not fps or fps <= 0:
                logger.warning(f"OpenCV未能获取 '{video_path.name}' 的有效FPS，尝试使用ffprobe备用方案...")
                fps = self._get_fps_with_ffprobe(video_path)
                if fps and fps > 0:
                    logger.info(f"✅ ffprobe备用方案成功。检测到FPS: {fps:.3f}")

            # 获取其他信息
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

            # 使用最终确定的fps计算时长
            duration = frame_count / fps if fps and fps > 0 else 0

            cap.release()

            return {
                "duration": duration,
                "fps": fps,
                "frame_count": frame_count,
                "width": width,
                "height": height,
                "size": video_path.stat().st_size,
            }

        except Exception as e:
            logger.error(f"获取视频信息失败: {e}")
            return {}

    def get_media_info(self, media_path: Path) -> dict:
        """获取媒体文件（视频或音频）的基本信息"""
        if not media_path.exists():
            logger.error(f"媒体文件不存在: {media_path}")
            return {}

        # 对视频文件使用OpenCV，因为它能提供更全面的信息
        if media_path.suffix.lower() in [".mp4", ".mov", ".avi", ".mkv"]:
            return self.get_video_info(media_path)

        # 对音频文件使用ffprobe，这是一个更可靠的方式
        try:
            cmd = [
                "ffprobe",
                "-v",
                "error",
                "-show_entries",
                "format=duration",
                "-of",
                "default=noprint_wrappers=1:nokey=1",
                str(media_path),
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            duration = float(result.stdout.strip())
            return {"duration": duration}
        except (subprocess.CalledProcessError, ValueError, FileNotFoundError) as e:
            logger.error(f"使用ffprobe获取音频信息失败: {media_path}, 错误: {e}")
            return {}

    def extract_clip(self, video_path: Path, start_time: float, end_time: float, output_path: Path) -> bool:
        """
        使用ffmpeg精确提取视频片段（包含音视频），并支持中断。
        """
        try:
            cmd = [
                "ffmpeg",
                "-ss",
                str(start_time),
                "-to",
                str(end_time),
                "-i",
                str(video_path),
                "-c:v",
                "libx264",
                "-preset",
                "fast",
                "-c:a",
                "aac",
                "-ac",
                "2",
                "-y",
                str(output_path),
            ]
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

            while process.poll() is None:
                if shutdown_event.is_set():
                    logger.warning(f"检测到关闭信号，正在终止ffmpeg进程 (PID: {process.pid})")
                    process.terminate()
                    process.wait(timeout=5)
                    return False
                time.sleep(0.1)

            if process.returncode == 0:
                logger.debug(f"成功提取片段: {output_path}")
                return True
            else:
                stdout, stderr = process.communicate()
                logger.error(f"提取片段失败 (ffmpeg): {stderr}")
                return False
        except Exception as e:
            logger.error(f"提取片段时发生未知错误: {e}")
            return False

    def create_low_quality_video(self, source_clip_path: Path, lq_video_path: Path, video_bitrate: str) -> bool:
        """
        从一个高画质视频片段中，创建用于AI分析的低画质、无声视频。
        """
        try:
            lq_cmd = [
                "ffmpeg",
                "-i",
                str(source_clip_path),
                "-c:v",
                "libx264",
                "-b:v",
                video_bitrate,
                "-vf",
                "scale=-2:540",
                "-an",  # 无声
                "-y",
                str(lq_video_path),
            ]
            lq_result = subprocess.run(lq_cmd, capture_output=True, text=True, check=False)
            if lq_result.returncode != 0:
                logger.error(f"创建低质量视频失败: {lq_result.stderr}")
                return False
            logger.debug(f"成功创建低质量视频: {lq_video_path}")
            return True
        except Exception as e:
            logger.error(f"创建分析资产时出错: {e}")
            return False

    def extract_full_audio(self, video_path: Path, output_audio_path: Path, audio_bitrate: str) -> bool:
        """从完整的视频文件中提取音轨。"""
        try:
            audio_cmd = [
                "ffmpeg",
                "-i",
                str(video_path),
                "-vn",  # 无视频
                "-c:a",
                "mp3",
                "-b:a",
                audio_bitrate,
                "-y",
                str(output_audio_path),
            ]
            result = subprocess.run(audio_cmd, capture_output=True, text=True, check=False)
            if result.returncode != 0:
                # 某些视频可能没有音轨，这不应视为致命错误
                logger.warning(f"提取音频时可能出错 (或视频无音轨): {result.stderr}")
            logger.info(f"成功提取完整音轨到: {output_audio_path}")
            return True
        except Exception as e:
            logger.error(f"提取完整音轨时出错: {e}")
            return False

    def get_frame_at_time(self, video_path: Path, time_seconds: float) -> Optional[bytes]:
        """
        获取指定时间的视频帧 (使用 OpenCV，与人脸检测时保持一致)
        """
        if not video_path.exists():
            logger.error(f"视频文件不存在: {video_path}")
            return None
        try:
            cap = cv2.VideoCapture(str(video_path))
            if not cap.isOpened():
                logger.error(f"无法使用OpenCV打开视频文件: {video_path}")
                return None

            fps = cap.get(cv2.CAP_PROP_FPS)
            if fps <= 0:
                logger.error(f"视频文件FPS为0或无效: {video_path}")
                cap.release()
                return None

            # 计算目标帧的编号
            frame_number = int(time_seconds * fps)
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)

            # 读取帧
            ret, frame = cap.read()
            cap.release()

            if ret:
                # 将帧编码为JPEG字节流，以便后续处理
                is_success, buffer = cv2.imencode(".jpg", frame)
                if is_success:
                    return buffer.tobytes()
                else:
                    logger.error("使用cv2.imencode将帧编码为jpg失败。")
                    return None
            else:
                logger.warning(f"无法在 {time_seconds:.2f}s (帧号 {frame_number}) 读取到有效帧。")
                return None

        except Exception as e:
            logger.error(f"使用OpenCV获取视频帧时发生未知错误: {e}", exc_info=True)
            return None

    def extract_and_save_face_image(
        self, video_path: Path, time_seconds: float, bounding_box: Dict[str, int], output_path: Path
    ) -> bool:
        """从视频的指定时间点提取完整帧，并在人脸位置绘制矩形框后保存。"""
        frame_bytes = self.get_frame_at_time(video_path, time_seconds)
        if not frame_bytes:
            logger.error(f"无法在 {time_seconds:.2f}s 获取帧。")
            return False

        try:
            # 将字节解码为图像
            nparr = np.frombuffer(frame_bytes, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

            # 获取边界框坐标
            x, y, w, h = (
                bounding_box["x"],
                bounding_box["y"],
                bounding_box["w"],
                bounding_box["h"],
            )

            # 在完整的图像上绘制矩形框
            # (x, y) 是左上角坐标，(x+w, y+h) 是右下角坐标
            # (0, 255, 0) 是绿色，2 是线条粗细
            cv2.rectangle(img, (x, y), (x + w, y + h), (0, 255, 0), 2)

            # 保存带有矩形框的完整帧图像
            output_path.parent.mkdir(parents=True, exist_ok=True)
            cv2.imwrite(str(output_path), img)
            logger.debug(f"已保存带有人脸框的代表性帧图像到: {output_path}")
            return True
        except Exception as e:
            logger.error(f"绘制人脸框并保存帧图像时出错: {e}")
            return False

    def merge_audio_segments(self, audio_paths: List[Path], output_path: Path) -> Optional[Tuple[Path, List[float]]]:
        """
        将多个音频片段合并成一个文件，并返回合并后的路径及每个原始片段的时长列表。
        """
        if not audio_paths:
            return None

        segment_durations = [self.get_media_info(p).get("duration", 0) for p in audio_paths]

        list_file = output_path.with_suffix(".audiolist.txt")
        try:
            with open(list_file, "w", encoding="utf-8") as f:
                for path in audio_paths:
                    f.write(f"file '{path.resolve().as_posix()}'\n")

            cmd = ["ffmpeg", "-f", "concat", "-safe", "0", "-i", str(list_file), "-c", "copy", "-y", str(output_path)]
            subprocess.run(cmd, check=True, capture_output=True, text=True)
            logger.info(f"成功合并 {len(audio_paths)} 个音频片段到: {output_path}")
            return output_path, segment_durations
        except subprocess.CalledProcessError as e:
            logger.error(f"合并音频失败: {e.stderr}")
            return None
        except Exception as e:
            logger.error(f"合并音频时发生未知错误: {e}")
            return None
        finally:
            if list_file.exists():
                list_file.unlink()

    def _create_filter_element(self, parent_clipitem: ET.Element):
        """为视频剪辑创建包含基本运动、裁剪和不透明度效果的 <filter> 元素。"""
        filter_el = ET.SubElement(parent_clipitem, "filter")
        effect = ET.SubElement(filter_el, "effect")
        ET.SubElement(effect, "name").text = "Basic Motion"
        ET.SubElement(effect, "effectid").text = "basic"
        ET.SubElement(effect, "effectcategory").text = "motion"
        ET.SubElement(effect, "effecttype").text = "motion"
        ET.SubElement(effect, "mediatype").text = "video"
        # ... (此处可以添加具体的参数，但对于兼容性，仅结构就已足够)

        filter_el = ET.SubElement(parent_clipitem, "filter")
        effect = ET.SubElement(filter_el, "effect")
        ET.SubElement(effect, "name").text = "Crop"
        ET.SubElement(effect, "effectid").text = "crop"
        ET.SubElement(effect, "effectcategory").text = "motion"
        ET.SubElement(effect, "effecttype").text = "filter"
        ET.SubElement(effect, "mediatype").text = "video"

        filter_el = ET.SubElement(parent_clipitem, "filter")
        effect = ET.SubElement(filter_el, "effect")
        ET.SubElement(effect, "name").text = "Opacity"
        ET.SubElement(effect, "effectid").text = "opacity"
        ET.SubElement(effect, "effectcategory").text = "motion"
        ET.SubElement(effect, "effecttype").text = "filter"
        ET.SubElement(effect, "mediatype").text = "video"
        param = ET.SubElement(effect, "parameter")
        ET.SubElement(param, "parameterid").text = "opacity"
        ET.SubElement(param, "name").text = "opacity"
        value_el = ET.SubElement(param, "value")
        value_el.text = "100"

    def _create_media_characteristics_element(self, media_type: str, info: dict, timebase: int) -> ET.Element:
        """辅助函数：创建 <samplecharacteristics> XML 元素"""
        sample_char = ET.Element("samplecharacteristics")
        if media_type == "video":
            ET.SubElement(sample_char, "width").text = str(info.get("width", 1920))
            ET.SubElement(sample_char, "height").text = str(info.get("height", 1080))
            ET.SubElement(sample_char, "pixelaspectratio").text = "square"
            rate_el = ET.SubElement(sample_char, "rate")
            ET.SubElement(rate_el, "timebase").text = str(timebase)
            ET.SubElement(rate_el, "ntsc").text = "FALSE"
        elif media_type == "audio":
            ET.SubElement(sample_char, "depth").text = "16"
            ET.SubElement(sample_char, "samplerate").text = "48000"
        return sample_char

    def generate_fcp7_xml_from_timeline(
        self,
        video_events: List[Dict[str, Any]],
        output_path: Path,
        title: str = "AutoCutter Project",
        audio_events: Optional[List[Dict[str, Any]]] = None,
    ) -> bool:  # <-- 修改返回类型
        """
        根据详细的时间线事件列表（包括视频和音频），生成一个 FCP7 Legacy XML 文件。
        【新功能】：支持双视频轨道交错剪辑和独立的旁白音轨事件。
        """
        logger.info("开始根据时间线事件生成 Final Cut Pro 7 Legacy XML 文件...")
        if not video_events:
            logger.error("没有提供视频事件，无法生成XML。")
            return False # <-- 修改返回

        try:
            # --- XML 基础结构设置 (与之前相同) ---
            first_clip_info = self.get_video_info(video_events[0]["path"])
            fps = first_clip_info.get("fps")
            if not fps or fps <= 0:
                logger.error("无法获取视频基准帧率，无法生成XML。")
                return False

            timebase = int(round(fps))
            if abs(fps - 23.976) < 0.1:
                timebase = 24
            elif abs(fps - 29.97) < 0.1:
                timebase = 30
            elif abs(fps - 59.94) < 0.1:
                timebase = 60

            xmeml = ET.Element("xmeml", version="5")
            sequence = ET.SubElement(xmeml, "sequence", id="sequence-1")
            ET.SubElement(sequence, "name").text = title

            # 计算总时长时，需要同时考虑视频和音频事件
            max_video_end = max(e["timeline_end"] for e in video_events) if video_events else 0
            max_audio_end = max(e["timeline_end"] for e in audio_events) if audio_events else 0
            total_duration_sec = max(max_video_end, max_audio_end)
            total_duration_frames = int(total_duration_sec * timebase)

            ET.SubElement(sequence, "duration").text = str(total_duration_frames)
            ET.SubElement(sequence, "in").text = "-1"
            ET.SubElement(sequence, "out").text = "-1"
            rate = ET.SubElement(sequence, "rate")
            ET.SubElement(rate, "timebase").text = str(timebase)
            ET.SubElement(rate, "ntsc").text = "FALSE"
            tc = ET.SubElement(sequence, "timecode")
            tc_rate = ET.SubElement(tc, "rate")
            ET.SubElement(tc_rate, "timebase").text = str(timebase)
            ET.SubElement(tc_rate, "ntsc").text = "FALSE"
            ET.SubElement(tc, "string").text = "00:00:00:00"
            ET.SubElement(tc, "frame").text = "0"
            ET.SubElement(tc, "displayformat").text = "NDF"
            media = ET.SubElement(sequence, "media")

            # --- 轨道定义 (核心改动) ---
            video = ET.SubElement(media, "video")
            video_format = ET.SubElement(video, "format")
            video_format.append(self._create_media_characteristics_element("video", first_clip_info, timebase))

            max_video_track_index = max(e.get("track_index", 0) for e in video_events) if video_events else 0
            video_tracks = [ET.SubElement(video, "track") for _ in range(max_video_track_index + 1)]

            audio = ET.SubElement(media, "audio")
            audio_format = ET.SubElement(audio, "format")
            audio_format.append(self._create_media_characteristics_element("audio", {}, timebase))

            # 【核心修改】为每个视频轨道创建一个对应的音频轨道，并为旁白创建一个专用轨道
            num_video_tracks = max_video_track_index + 1
            audio_tracks_for_video = [ET.SubElement(audio, "track") for _ in range(num_video_tracks * 2)]
            track_narration = ET.SubElement(audio, "track")  # 旁白轨道

            def _add_link(parent, ref_id, media_type, trk_idx, clip_idx=1):
                ln = ET.SubElement(parent, "link")
                ET.SubElement(ln, "linkclipref").text = ref_id
                ET.SubElement(ln, "mediatype").text = media_type
                ET.SubElement(ln, "trackindex").text = str(trk_idx)
                ET.SubElement(ln, "clipindex").text = str(clip_idx)

            # --- 填充视频轨道和对应音频轨道 (与之前相同) ---
            for i, event in enumerate(video_events):
                track_index = event.get("track_index", 0)
                target_v_track = video_tracks[track_index]
                clipitem_v_id = f"v-clip-{i + 1}"
                clipitem_v = ET.SubElement(target_v_track, "clipitem", id=clipitem_v_id)
                self._create_filter_element(clipitem_v)
                ET.SubElement(clipitem_v, "name").text = event["path"].name
                ET.SubElement(clipitem_v, "duration").text = str(int(event["timeline_duration"] * timebase))
                ET.SubElement(clipitem_v, "start").text = str(int(event["timeline_start"] * timebase))
                ET.SubElement(clipitem_v, "end").text = str(int(event["timeline_end"] * timebase))
                ET.SubElement(clipitem_v, "in").text = str(int(event["source_in"] * timebase))
                ET.SubElement(clipitem_v, "out").text = str(int(event["source_out"] * timebase))
                file_el_id = f"file-v-{i + 1}"
                file_el = ET.SubElement(clipitem_v, "file", id=file_el_id)
                ET.SubElement(file_el, "name").text = event["path"].name
                ET.SubElement(file_el, "pathurl").text = event["path"].resolve().as_uri()
                file_tc = ET.SubElement(file_el, "timecode")
                file_tc_rate = ET.SubElement(file_tc, "rate")
                ET.SubElement(file_tc_rate, "timebase").text = str(timebase)
                ET.SubElement(file_tc_rate, "ntsc").text = "FALSE"
                ET.SubElement(file_tc, "string").text = "00:00:00:00"
                ET.SubElement(file_tc, "frame").text = "0"
                ET.SubElement(file_tc, "displayformat").text = "NDF"
                file_rate = ET.SubElement(file_el, "rate")
                ET.SubElement(file_rate, "timebase").text = str(timebase)
                file_info = self.get_video_info(event["path"])
                file_duration_frames = int(file_info.get("duration", 0) * timebase)
                ET.SubElement(file_el, "duration").text = str(file_duration_frames)
                file_media = ET.SubElement(file_el, "media")
                file_video = ET.SubElement(file_media, "video")
                file_video.append(self._create_media_characteristics_element("video", file_info, timebase))
                file_audio_for_video = ET.SubElement(file_media, "audio")
                ET.SubElement(file_audio_for_video, "channelcount").text = "2"
                _add_link(clipitem_v, clipitem_v_id, "video", track_index + 1)

                # 根据 use_original_audio 标志决定是否包含原声音轨
                if event.get("use_original_audio", False):
                    # 为左右声道创建两个独立的音频clipitem
                    for channel in range(2):  # 0 for Left (Track 1), 1 for Right (Track 2)
                        # 确定目标音轨
                        # 视频轨道 0 对应音轨 1,2
                        # 视频轨道 1 对应音轨 3,4
                        # ...以此类推
                        target_a_track = audio_tracks_for_video[track_index * 2 + channel]
                        clipitem_a_id = f"a-clip-{i + 1}-ch{channel + 1}"
                        clipitem_a = ET.SubElement(target_a_track, "clipitem", id=clipitem_a_id)

                        # 将音频效果应用到两个声道
                        ducking_info = event.get("ducking_info")
                        if ducking_info:
                            from config.settings import settings
                            self._add_keyframed_audio_level_filter(
                                clipitem_a,
                                ducking_info,
                                event["timeline_start"],
                                timebase,
                                settings.AUDIO_DUCKING_DB,
                            )
                            if channel == 0:
                                logger.debug(f"为片段 {event['path'].name} 应用了关键帧音频闪避。")
                        # 若没有 ducking_info（普通原声），保持默认 0 dB，不再插入任何 Audio Levels 过滤器


                        ET.SubElement(clipitem_a, "name").text = event["path"].name
                        ET.SubElement(clipitem_a, "duration").text = str(int(event["timeline_duration"] * timebase))
                        ET.SubElement(clipitem_a, "start").text = str(int(event["timeline_start"] * timebase))
                        ET.SubElement(clipitem_a, "end").text = str(int(event["timeline_end"] * timebase))
                        ET.SubElement(clipitem_a, "in").text = str(int(event["source_in"] * timebase))
                        ET.SubElement(clipitem_a, "out").text = str(int(event["source_out"] * timebase))
                        
                        # 使用 sourcetrack 指定使用源文件的哪个音轨
                        sourcetrack = ET.SubElement(clipitem_a, "sourcetrack")
                        ET.SubElement(sourcetrack, "mediatype").text = "audio"
                        ET.SubElement(sourcetrack, "trackindex").text = str(channel + 1)

                        # 更新链接
                        audio_track_xml_index = track_index * 2 + channel + 1 # FCP7 音轨从1开始计数
                        _add_link(clipitem_v, clipitem_a_id, "audio", audio_track_xml_index)
                        _add_link(clipitem_a, clipitem_a_id, "audio", audio_track_xml_index)
                        _add_link(clipitem_a, clipitem_v_id, "video", track_index + 1)

                        # 文件引用
                        file_a = ET.SubElement(clipitem_a, "file", id=file_el_id)
                        ET.SubElement(file_a, "name").text = event["path"].name
                        ET.SubElement(file_a, "pathurl").text = event["path"].resolve().as_uri()
                        rate_a = ET.SubElement(file_a, "rate")
                        ET.SubElement(rate_a, "timebase").text = str(timebase)
                        file_duration_frames_a = int(file_info.get("duration", 0) * timebase)
                        ET.SubElement(file_a, "duration").text = str(file_duration_frames_a)
                        media_a = ET.SubElement(file_a, "media")
                        media_audio = ET.SubElement(media_a, "audio")
                        ET.SubElement(media_audio, "channelcount").text = "2"
                        media_audio.append(self._create_media_characteristics_element("audio", {}, timebase))
                else:
                    logger.debug(f"片段 {event['path'].name} 被标记为不使用原声，跳过音频clipitem生成。")

            # --- 【核心修改】填充旁白音轨 ---
            if audio_events:
                # 旁白音轨的索引在所有视频音轨之后
                narration_track_index = num_video_tracks * 2 + 1
                for i, event in enumerate(audio_events):
                    # 【核心修复】将字符串路径转换为 Path 对象
                    if not event.get("path"):
                        continue
                    audio_path = Path(event["path"])

                    audio_info = self.get_media_info(audio_path)
                    if not audio_info or audio_info.get("duration", 0) <= 0:
                        logger.warning(f"无法获取音频文件信息或时长为0，跳过: {audio_path.name}")
                        continue

                    clipitem_id = f"narration-clip-{i + 1}"
                    clipitem_a = ET.SubElement(track_narration, "clipitem", id=clipitem_id)
                    ET.SubElement(clipitem_a, "name").text = audio_path.name
                    ET.SubElement(clipitem_a, "duration").text = str(int(event["timeline_duration"] * timebase))
                    ET.SubElement(clipitem_a, "start").text = str(int(event["timeline_start"] * timebase))
                    ET.SubElement(clipitem_a, "end").text = str(int(event["timeline_end"] * timebase))
                    ET.SubElement(clipitem_a, "in").text = str(int(event["source_in"] * timebase))
                    ET.SubElement(clipitem_a, "out").text = str(int(event["source_out"] * timebase))

                    file_id = f"narration-file-{i + 1}"
                    file_a = ET.SubElement(clipitem_a, "file", id=file_id)
                    ET.SubElement(file_a, "name").text = audio_path.name
                    ET.SubElement(file_a, "pathurl").text = audio_path.resolve().as_uri()
                    file_a_rate = ET.SubElement(file_a, "rate")
                    ET.SubElement(file_a_rate, "timebase").text = str(timebase)
                    file_duration_frames = int(audio_info.get("duration", 0) * timebase)
                    ET.SubElement(file_a, "duration").text = str(file_duration_frames)
                    file_a_media = ET.SubElement(file_a, "media")
                    file_a_audio = ET.SubElement(file_a_media, "audio")
                    ET.SubElement(file_a_audio, "channelcount").text = "2"
                    file_a_audio.append(self._create_media_characteristics_element("audio", {}, timebase))

                    # 添加自引用链接
                    _add_link(clipitem_a, clipitem_id, "audio", narration_track_index, i + 1)

            # --- 格式化并写入文件 (与之前相同) ---
            xml_str = ET.tostring(xmeml, encoding="unicode")
            dom = minidom.parseString(xml_str)
            pretty_xml_str = dom.toprettyxml(indent="  ")
            with open(output_path, "w", encoding="utf-8") as f:
                f.write(pretty_xml_str)
            logger.info(f"成功生成 FCP 7 Legacy XML 文件: {output_path}")
            return True  # <-- 新增成功返回

        except Exception as e:
            logger.error(f"生成 FCP 7 XML 文件时出错: {e}", exc_info=True)
            return False  # <-- 新增失败返回

    def delete_file(self, file_path: Path):
        """
        删除指定路径的文件。
        """
        try:
            if file_path.exists():
                file_path.unlink()
                logger.debug(f"成功删除文件: {file_path}")
                return True
            else:
                logger.warning(f"尝试删除的文件不存在: {file_path}")
                return False
        except Exception as e:
            logger.error(f"删除文件 {file_path} 时出错: {e}")
            return False


# 全局视频处理器实例
video_processor = VideoProcessor()
