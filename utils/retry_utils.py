"""
通用重试工具模块
"""
import time
from functools import wraps
from config.settings import settings
from utils.logger import get_logger

logger = get_logger(__name__)


def retry_on_exception(max_retries=settings.MAX_RETRIES, delay=settings.RETRY_DELAY):
    """
    一个装饰器，用于为任何函数或方法添加重试逻辑。
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    logger.warning(f"调用 {func.__name__} 失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                    if attempt < max_retries - 1:
                        time.sleep(delay)
                    else:
                        logger.error(f"调用 {func.__name__} 最终失败。")
                        raise
            # This part should ideally not be reached
            raise RuntimeError(f"{func.__name__} 调用意外结束，未返回结果也未抛出异常")
        return wrapper
    return decorator
