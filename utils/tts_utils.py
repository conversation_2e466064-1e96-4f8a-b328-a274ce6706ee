"""
TTS (文本转语音) 工具模块
使用 edge-tts 提供高质量的文本转语音功能。
"""

import asyncio
from pathlib import Path
import edge_tts

from utils.logger import get_logger
from utils.retry_utils import retry_on_exception

logger = get_logger(__name__)


class EdgeTTSClient:
    """
    封装 edge-tts 库，提供简单的文本到音频文件转换接口。
    """
    
    # 将用户友好的名称映射到edge-tts所需的完整语音名称
    VOICE_MAP = {
        "xiaoxiao": "zh-CN-XiaoxiaoNeural",
        "yunxi": "zh-CN-YunxiNeural",
    }

    def __init__(self):
        logger.info("EdgeTTSClient 初始化完成。")

    async def _generate_async(self, text: str, voice: str, output_path: Path):
        """异步生成音频的核心方法"""
        communicate = edge_tts.Communicate(text, voice, rate="+20%")
        await communicate.save(str(output_path))

    @retry_on_exception()
    def generate_audio(self, text: str, output_path: Path, voice_name: str = "xiaoxiao") -> bool:
        """
        调用edge-tts将文本转换为音频文件。
        这是一个同步方法，它在内部运行异步核心。

        :param text: 要转换的文本。
        :param output_path: 音频文件的保存路径。
        :param voice_name: 发音人名称 ('xiaoxiao' 或 'yunxi')。
        :return: 如果成功则返回True，否则返回False。
        """
        full_voice_name = self.VOICE_MAP.get(voice_name)
        if not full_voice_name:
            logger.error(f"无效的发音人名称: {voice_name}。请从 {list(self.VOICE_MAP.keys())} 中选择。")
            # 抛出异常以确保重试逻辑不会认为这是成功
            raise ValueError(f"无效的发音人名称: {voice_name}")

        logger.info(f"正在使用发音人 '{full_voice_name}' 为文本 '{text[:20]}...' 生成配音...")
        # 从同步代码中运行异步函数
        asyncio.run(self._generate_async(text, full_voice_name, output_path))
        
        logger.info(f"成功生成配音文件: {output_path}")
        return True


_tts_client_instance = None

def get_tts_client() -> EdgeTTSClient:
    """获取EdgeTTSClient的单例。在首次调用时初始化。"""
    global _tts_client_instance
    if _tts_client_instance is None:
        _tts_client_instance = EdgeTTSClient()
    return _tts_client_instance
