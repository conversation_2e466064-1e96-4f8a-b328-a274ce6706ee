"""
对象存储工具模块
使用 volcengine 官方 tos-python-sdk 提供与TOS交互的功能。
"""

import tos
from pathlib import Path
from typing import Optional
from config.settings import settings
from utils.logger import get_logger

logger = get_logger(__name__)


class TOSClient:
    """TOS客户端封装 (使用官方 tos-python-sdk)"""

    def __init__(self):
        self.bucket = settings.TOS_BUCKET
        self.endpoint = settings.TOS_ENDPOINT
        try:
            self.client = tos.TosClientV2(
                ak=settings.TOS_ACCESS_KEY,
                sk=settings.TOS_SECRET_KEY,
                endpoint=self.endpoint,
                region=settings.TOS_REGION,
            )
            logger.info("TOSClient 初始化成功 (使用官方 tos-python-sdk)。")
        except Exception as e:
            logger.error(f"初始化 TOSClient 失败: {e}")
            self.client = None

    def head_bucket(self, bucket: str):
        """
        检查存储桶是否存在且可访问 (通过 HeadBucket API)。
        如果验证失败，SDK会抛出异常。
        """
        if not self.client:
            raise ConnectionError("TOS client未初始化")
        self.client.head_bucket(bucket)

    def object_exists(self, object_key: str) -> bool:
        """
        使用 head_object 检查对象是否存在于TOS存储桶中。
        :param object_key: 对象的键
        :return: 如果存在则返回True，否则返回False
        """
        if not self.client:
            return False
        try:
            resp = self.client.head_object(self.bucket, object_key)
            # 如果对象存在，head_object会返回200 OK
            return resp.status_code == 200
        except tos.exceptions.TosServerError as e:
            # 如果对象不存在，SDK会抛出404 Not Found的异常
            if e.status_code == 404:
                return False
            # 其他服务端错误则重新抛出，让上层处理
            logger.error(f"检查TOS对象 '{object_key}' 时发生服务端错误: {e}")
            raise
        except Exception as e:
            logger.error(f"检查TOS对象 '{object_key}' 时发生未知错误: {e}")
            raise  # 对于未知错误，也应该让程序中断，而不是静默失败

    def upload_file(self, file_path: Path, object_key: str) -> Optional[str]:
        """
        上传本地文件到TOS，返回公开URL。如果文件已存在，则跳过上传。
        :param file_path: 本地文件路径
        :param object_key: 在存储桶中的对象名称/路径
        :return: 文件的公开URL，如果失败则返回None
        """
        if not self.client:
            logger.error("TOS 客户端未初始化，无法上传。")
            return None

        # 提前构建好公开URL
        public_url = f"https://{self.bucket}.{self.endpoint.replace('https://', '')}/{object_key}"

        try:
            # 步骤 1: 检查文件是否已存在
            if self.object_exists(object_key):
                logger.debug(f"文件已存在于TOS，跳过上传: {object_key}")
                return public_url
        except Exception:
            # 如果检查失败，记录警告但继续尝试上传，保证流程鲁棒性
            logger.warning(f"检查TOS文件存在性失败，将尝试直接上传: {object_key}")

        # 步骤 2: 上传文件
        if not file_path.exists():
            logger.error(f"要上传的本地文件未找到: {file_path}")
            return None

        try:
            logger.debug(f"开始上传 {file_path.name} 到 {self.bucket}/{object_key}")

            # 使用 put_object_from_file 方法上传文件
            resp = self.client.put_object_from_file(self.bucket, object_key, str(file_path))

            if resp.status_code == 200:
                logger.info(f"文件上传成功: {public_url}")
                return public_url
            else:
                logger.error(f"上传文件到TOS返回失败的状态码: {resp.status_code}, Request-ID: {resp.request_id}")
                return None

        except Exception as e:
            logger.error(f"上传文件到TOS时出错: {e}")
            return None


_tos_client_instance = None

def get_tos_client() -> TOSClient:
    """获取TOSClient的单例。在首次调用时初始化。"""
    global _tos_client_instance
    if _tos_client_instance is None:
        _tos_client_instance = TOSClient()
    return _tos_client_instance
