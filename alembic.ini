# Alembic 的主配置文件。

[alembic]
# 指向包含迁移脚本的目录。这是最重要的配置。
script_location = alembic

# 数据库连接字符串。
# 使用 ${ENV_VAR} 语法，Alembic 会自动从环境变量（或 .env 文件）中读取这些值。
# 这是安全处理数据库凭证的最佳实践。
sqlalchemy.url = postgresql+psycopg2://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}

# 迁移脚本文件名的模板。
# file_template = %%(rev)s_%%(slug)s

# 其他配置...
# timezone = UTC
# output_encoding = utf-8


[post_write_hooks]
# 这是一个非常有用的钩子，可以在生成新的迁移文件后自动运行代码格式化工具。
# 如果你想使用，请取消注释并确保 ruff 已安装。
# hooks = ruff
# ruff.type = ruff
# ruff.autofix = true


# 以下是日志记录配置，通常保持默认即可。
# 它能让你在运行 alembic 命令时在控制台看到详细的输出。

[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = WARN
handlers = console
qualname =

[logger_sqlalchemy]
level = WARN
handlers =
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handlers =
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %(levelname)-5.5s [%(name)s] %(message)s
datefmt = %H:%M:%S
