"""
配置管理模块
负责加载和管理所有配置参数
"""

import os
from pathlib import Path

from dotenv import load_dotenv
from utils.logger import get_logger

load_dotenv()


class Settings:
    def __init__(self):
        self.PROJECT_ROOT = Path(__file__).parent.parent
        self.logger = get_logger(self.__class__.__name__)

        # --- 新增：调试模式开关 ---
        # 如果设置为大于0的整数，则 Stage16 和 Stage18 将只处理前 N 个场景。
        # 设置为 0 或留空则处理所有场景。
        self.DEBUG_MODE_SCENE_LIMIT = int(os.getenv("DEBUG_MODE_SCENE_LIMIT", "0"))

        # --- 数据库配置 (PostgreSQL) ---
        self.POSTGRES_HOST = os.getenv("POSTGRES_HOST", "localhost")
        self.POSTGRES_PORT = int(os.getenv("POSTGRES_PORT", "5432"))
        self.POSTGRES_DB = os.getenv("POSTGRES_DB", "autocutter")
        self.POSTGRES_USER = os.getenv("POSTGRES_USER", "postgres")
        self.POSTGRES_PASSWORD = os.getenv("POSTGRES_PASSWORD", "")

        # 视频处理配置
        self.INPUT_DIR = self.PROJECT_ROOT / "input"
        self.CLIPS_DIR = self.PROJECT_ROOT / "clips"
        self.OUTPUT_DIR = self.PROJECT_ROOT / "output"

        api_keys_str = os.getenv("VOLCENGINE_API_KEY", "")
        self.VOLCENGINE_API_KEYS = [key.strip() for key in api_keys_str.split(",") if key.strip()]

        self.VOLCENGINE_BASE_URL = os.getenv("VOLCENGINE_BASE_URL", "https://ark.cn-beijing.volces.com/api/v3")
        self.VOLCENGINE_MODEL_NAME = os.getenv("VOLCENGINE_MODEL_NAME", "doubao-seed-1-6-250615")

        # --- 新增：GROK API 配置 ---
        grok_api_keys_str = os.getenv("GROK_API_KEY", "")
        self.GROK_API_KEYS = [key.strip() for key in grok_api_keys_str.split(",") if key.strip()]
        self.GROK_BASE_URL = os.getenv("GROK_BASE_URL", "https://api.ephone.chat/v1")
        self.GROK_MODEL_NAME = os.getenv("GROK_MODEL_NAME", "grok-3-deepsearch")
        self.GROK_TIMEOUT = int(os.getenv("GROK_TIMEOUT", "600"))

        # --- 新增：高级分析AI配置 ---
        advanced_api_keys_str = os.getenv("ADVANCED_AI_API_KEY", "")
        self.ADVANCED_AI_API_KEYS = [key.strip() for key in advanced_api_keys_str.split(",") if key.strip()]
        self.ADVANCED_AI_BASE_URL = os.getenv("ADVANCED_AI_BASE_URL")
        self.ADVANCED_AI_MODEL_NAME = os.getenv("ADVANCED_AI_MODEL_NAME")
        self.ADVANCED_AI_EMBEDDING_MODEL = os.getenv("ADVANCED_AI_EMBEDDING_MODEL", "text-embedding-3-small")
        self.ADVANCED_AI_TIMEOUT = int(os.getenv("ADVANCED_AI_TIMEOUT", "600"))

        # --- 新增：最高级分析AI配置 ---
        most_advanced_api_keys_str = os.getenv("MOST_ADVANCED_AI_API_KEY", "")
        self.MOST_ADVANCED_AI_API_KEYS = [key.strip() for key in most_advanced_api_keys_str.split(",") if key.strip()]
        self.MOST_ADVANCED_AI_BASE_URL = os.getenv("MOST_ADVANCED_AI_BASE_URL")
        self.MOST_ADVANCED_AI_MODEL_NAME = os.getenv("MOST_ADVANCED_AI_MODEL_NAME")
        self.MOST_ADVANCED_AI_TIMEOUT = int(os.getenv("MOST_ADVANCED_AI_TIMEOUT", "600"))

        # --- 新增：本地嵌入模型配置 ---
        self.EMBEDDING_API_BASE_URL = os.getenv("EMBEDDING_API_BASE_URL")
        self.EMBEDDING_MODEL_NAME = os.getenv("EMBEDDING_MODEL_NAME")
        self.EMBEDDING_API_KEY = os.getenv("EMBEDDING_API_KEY")
        self.EMBEDDING_BATCH_SIZE = int(os.getenv("EMBEDDING_BATCH_SIZE", "5"))  # 新增此行

        # 其他配置... (保持不变)
        self.TAVILY_API_KEY = os.getenv("TAVILY_API_KEY", "")
        self.TOS_ENDPOINT = os.getenv("TOS_ENDPOINT", "")
        self.TOS_ACCESS_KEY = os.getenv("TOS_ACCESS_KEY", "")
        self.TOS_SECRET_KEY = os.getenv("TOS_SECRET_KEY", "")
        self.TOS_REGION = os.getenv("TOS_REGION", "us-east-1")
        self.TOS_BUCKET = os.getenv("TOS_BUCKET", "")
        # --- TTS 配置 ---
        self.TTS_VOICE = os.getenv("TTS_VOICE", "yunxi")
        self.MAX_CONCURRENT_REQUESTS = int(os.getenv("MAX_CONCURRENT_REQUESTS", "5"))
        self.SCENE_DETECTION_THRESHOLD = float(os.getenv("SCENE_DETECTION_THRESHOLD", "30.0"))  # 场景检测阈值
        # 新增：场景检测器类型
        self.SCENE_DETECTOR = os.getenv("SCENE_DETECTOR", "adaptive")

        # 新增：加载并验证 scenedetect 预设
        self.SCENEDETECT_PRESET = os.getenv("SCENEDETECT_PRESET", "ultrafast")
        valid_presets = ["ultrafast", "superfast", "veryfast", "faster", "fast", "medium", "slow", "slower", "veryslow"]
        if self.SCENEDETECT_PRESET not in valid_presets:
            self.logger.warning(f"无效的 SCENEDETECT_PRESET: '{self.SCENEDETECT_PRESET}'。将回退到默认的 'ultrafast'。")
            self.SCENEDETECT_PRESET = "ultrafast"

        # 新增：用于AI分析的低质量视频比特率
        self.LOW_QUALITY_BITRATE = os.getenv("LOW_QUALITY_BITRATE", "500k")
        # 新增：提取的音频比特率
        self.AUDIO_BITRATE = os.getenv("AUDIO_BITRATE", "96k")
        # 新增：AI分析视频帧率
        self.SCENE_VIDEO_FPS = int(os.getenv("SCENE_VIDEO_FPS", "5"))
        # 新增：AI分析视频细节级别 ("low" 或 "high")
        self.SCENE_VIDEO_DETAIL = os.getenv("SCENE_VIDEO_DETAIL", "high")
        # 新增：FaceNet 预训练模型配置
        self.FACENET_PRETRAINED_MODEL = os.getenv("FACENET_PRETRAINED_MODEL", "vggface2")
        # 新增：被认为是有效人脸的最小像素尺寸
        self.MIN_FACE_SIZE = int(os.getenv("MIN_FACE_SIZE", "40"))
        if self.FACENET_PRETRAINED_MODEL not in ["vggface2", "casia-webface"]:
            self.logger.warning(
                f"无效的 FACENET_PRETRAINED_MODEL: '{self.FACENET_PRETRAINED_MODEL}'。将回退到默认的 'vggface2'。"
            )
            self.FACENET_PRETRAINED_MODEL = "vggface2"
        # 新增：FaceNet 人脸检测置信度阈值
        self.FACENET_CONFIDENCE_THRESHOLD = float(os.getenv("FACENET_CONFIDENCE_THRESHOLD", "0.9"))
        # 新增：人脸检测的视频帧采样率
        self.FACE_DETECTION_FRAME_SAMPLING_RATE = int(os.getenv("FACE_DETECTION_FRAME_SAMPLING_RATE", "5"))
        # 新增：【调试模式】如果为 "true"，将在人脸提取时保存中间图像
        self.FACE_DEBUG_MODE = os.getenv("FACE_DEBUG_MODE", "false").lower() == "true"
        # 新增：角色自动标注的匹配阈值
        self.CHARACTER_MATCHING_THRESHOLD = float(os.getenv("CHARACTER_MATCHING_THRESHOLD", "0.35"))
        # 新增：角色自动标注的建议阈值 (比硬匹配阈值宽松)
        self.CHARACTER_SUGGESTION_THRESHOLD = float(os.getenv("CHARACTER_SUGGESTION_THRESHOLD", "0.6"))
        # 新增：模糊检测阈值
        self.BLUR_DETECTION_THRESHOLD = float(os.getenv("BLUR_DETECTION_THRESHOLD", "100.0"))
        # 新增：DBSCAN 聚类配置
        self.DBSCAN_EPS = float(os.getenv("DBSCAN_EPS", "0.25"))
        self.DBSCAN_MIN_SAMPLES = int(os.getenv("DBSCAN_MIN_SAMPLES", "3"))
        # 新增：在交互式命名阶段，为每个角色显示的代表性人脸图片数量
        self.NAMING_FACE_COUNT = int(os.getenv("NAMING_FACE_COUNT", "3"))
        self.MAX_RETRIES = int(os.getenv("MAX_RETRIES", "3"))
        self.RETRY_DELAY = int(os.getenv("RETRY_DELAY", "1"))
        self.LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO").upper()

        # --- API 密钥冷却配置 ---
        self.API_KEY_COOLDOWN_SECONDS = int(os.getenv("API_KEY_COOLDOWN_SECONDS", "300"))  # 默认冷却5分钟

        # --- 旁白时长估算 ---
        self.WHISPER_MODEL = os.getenv("WHISPER_MODEL", "mlx-community/whisper-large-v3-turbo")
        self.WHISPER_LANGUAGE = os.getenv("WHISPER_LANGUAGE", "en")

        # 新增：VAD 配置
        self.VAD_THRESHOLD = float(os.getenv("VAD_THRESHOLD", "0.5"))
        self.VAD_MIN_SILENCE_DURATION_MS = int(os.getenv("VAD_MIN_SILENCE_DURATION_MS", "100"))
        self.VAD_MIN_SPEECH_DURATION_MS = int(os.getenv("VAD_MIN_SPEECH_DURATION_MS", "250"))

        # --- Shot ↔︎ Sentence 匹配策略 ---
        self.SHOT_SCORE_THRESHOLD = int(os.getenv("SHOT_SCORE_THRESHOLD", "8"))  # 首选阈值
        self.SHOT_SCORE_FALLBACK = int(os.getenv("SHOT_SCORE_FALLBACK", "6"))  # 降级阈值
        self.MAX_SHOTS_PER_SENTENCE = int(os.getenv("MAX_SHOTS_PER_SENTENCE", "5"))
        # 选中镜头总时长需达到 旁白预计朗读时长 × 本系数
        self.SHOTS_TOTAL_DURATION_FACTOR = float(os.getenv("SHOTS_TOTAL_DURATION_FACTOR", "1.1"))

        # --- 高级剪辑策略 ---
        # 镜头可以比对应的旁白音频提前多少秒开始
        self.SHOT_PRE_ROLL_SECONDS = float(os.getenv("SHOT_PRE_ROLL_SECONDS", "0.2"))
        # 镜头可以在对应的旁白音频结束后延迟多少秒结束
        self.SHOT_POST_ROLL_SECONDS = float(os.getenv("SHOT_POST_ROLL_SECONDS", "0.3"))
        # 相邻句子对应的视频片段之间，允许的最大重叠秒数，以创建J-Cut/L-Cut效果
        self.MAX_OVERLAP_SECONDS = float(os.getenv("MAX_OVERLAP_SECONDS", "0.5"))
        # 【新增】旁白出现时，原片音量降低的分贝数 (负值表示降低)
        self.AUDIO_DUCKING_DB = int(os.getenv("AUDIO_DUCKING_DB", "-18"))

        # 送给 AI 评分之前，最多保留多少个最相关镜头
        self.MAX_CANDIDATE_SHOTS_FOR_AI = int(os.getenv("MAX_CANDIDATE_SHOTS_FOR_AI", "15"))

        # --- 旁白时长估算 ---
        # 平均每秒朗读多少个中文字符（或英文单词的字符数近似）
        self.NARRATION_CHAR_PER_SEC = float(os.getenv("NARRATION_CHAR_PER_SEC", "6.0"))

        # 新增：剧本语言
        self.SCRIPT_LANGUAGE = os.getenv("SCRIPT_LANGUAGE", "中文")

        # ============== 智能剪辑优化配置 ==============

        # --- IMO方法相关配置 ---
        # 最大验证轮数（对应IMO的最大迭代次数）
        self.MAX_EDITING_REFINEMENT_PASSES = int(os.getenv("MAX_EDITING_REFINEMENT_PASSES", "5"))
        # 连续通过验证的要求次数
        self.CONSECUTIVE_PASS_REQUIREMENT = int(os.getenv("CONSECUTIVE_PASS_REQUIREMENT", "3"))
        # 验证通过的最低分数阈值（5分制）
        self.EDITING_PASS_THRESHOLD = float(os.getenv("EDITING_PASS_THRESHOLD", "4.0"))

        # --- 节奏优化配置 ---
        # 节奏模式定义
        self.RHYTHM_PATTERNS = {
            "fast_paced": {"avg_shot_duration": 2.0, "variance": 0.5},
            "balanced": {"avg_shot_duration": 3.5, "variance": 1.0},
            "slow_paced": {"avg_shot_duration": 5.0, "variance": 1.5},
        }

        # --- 时长优化配置 ---
        # 平台最优时长配置
        self.PLATFORM_DURATION_CONSTRAINTS = {
            "bilibili": {"optimal": (180, 300), "max": 600},  # 3-5分钟最优，10分钟内可接受
            "douyin": {"optimal": (15, 60), "max": 180},  # 15秒-1分钟最优，3分钟内可接受
        }

        # --- 质量评估配置 ---
        # 各维度评估权重
        self.EDITING_EVALUATION_WEIGHTS = {
            "duration_control": float(os.getenv("WEIGHT_DURATION_CONTROL", "0.25")),
            "material_matching": float(os.getenv("WEIGHT_MATERIAL_MATCHING", "0.30")),
            "rhythm_coherence": float(os.getenv("WEIGHT_RHYTHM_COHERENCE", "0.20")),
            "visual_coherence": float(os.getenv("WEIGHT_VISUAL_COHERENCE", "0.15")),
            "audio_video_sync": float(os.getenv("WEIGHT_AUDIO_VIDEO_SYNC", "0.10")),
        }

        # --- 智能镜头选择配置 ---
        # 镜头匹配度阈值
        self.HIGH_MATCH_THRESHOLD = float(os.getenv("HIGH_MATCH_THRESHOLD", "0.8"))
        self.MEDIUM_MATCH_THRESHOLD = float(os.getenv("MEDIUM_MATCH_THRESHOLD", "0.5"))
        # 时长匹配允许的偏差范围
        self.DURATION_MATCH_TOLERANCE = float(os.getenv("DURATION_MATCH_TOLERANCE", "0.2"))  # ±20%

        # --- 压缩策略配置 ---
        # 内容优先级权重
        self.CONTENT_PRIORITY_WEIGHTS = {
            "has_narration": float(os.getenv("WEIGHT_HAS_NARRATION", "0.5")),
            "high_confidence": float(os.getenv("WEIGHT_HIGH_CONFIDENCE", "0.3")),
            "emotional_climax": float(os.getenv("WEIGHT_EMOTIONAL_CLIMAX", "0.8")),
            "key_information": float(os.getenv("WEIGHT_KEY_INFORMATION", "0.6")),
        }

        # 最小镜头保留时长（秒）
        self.MIN_SHOT_DURATION = float(os.getenv("MIN_SHOT_DURATION", "0.5"))
        self.MAX_SHOT_DURATION = float(os.getenv("MAX_SHOT_DURATION", "10.0"))

        self._ensure_directories()

    def _ensure_directories(self):
        # 移除对 self.DATABASE_PATH.parent 的检查
        directories = [self.INPUT_DIR, self.CLIPS_DIR, self.OUTPUT_DIR]
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)

    def validate(self) -> list[str]:
        errors = []
        if not self.VOLCENGINE_API_KEYS:
            errors.append("VOLCENGINE_API_KEY 未设置或为空")

        # 验证 PostgreSQL 配置是否完整
        if not all([self.POSTGRES_DB, self.POSTGRES_USER, self.POSTGRES_PASSWORD]):
            errors.append("PostgreSQL 数据库配置不完整 (POSTGRES_DB, POSTGRES_USER, POSTGRES_PASSWORD 必须全部设置)")

        # ... (保留TOS验证逻辑)
        tos_configs = [self.TOS_ENDPOINT, self.TOS_ACCESS_KEY, self.TOS_SECRET_KEY, self.TOS_BUCKET]
        if any(tos_configs) and not all(tos_configs):
            errors.append("TOS 配置不完整")
        elif all(tos_configs):
            try:
                from utils.storage_utils import get_tos_client

                self.logger.info("正在验证TOS存储桶访问权限...")
                tos_client = get_tos_client()
                tos_client.head_bucket(bucket=self.TOS_BUCKET)
                self.logger.info("✅ TOS存储桶验证成功。")
            except Exception as e:
                errors.append(f"TOS 存储桶验证失败: {e}")

        return errors

    # ... (保留 get_ai_config 和 get_search_config 方法)
    def get_ai_config(self) -> dict:
        return {
            "api_keys": self.VOLCENGINE_API_KEYS,
            "base_url": self.VOLCENGINE_BASE_URL,
            "model": self.VOLCENGINE_MODEL_NAME,
        }

    def get_search_config(self) -> dict:
        return {"api_key": self.TAVILY_API_KEY}

    def get_tts_config(self) -> dict:
        """获取TTS的专属配置"""
        return {
            "voice": self.TTS_VOICE,
        }

    def get_grok_config(self) -> dict:
        """获取Grok AI的专属配置"""
        return {
            "api_keys": self.GROK_API_KEYS,
            "base_url": self.GROK_BASE_URL,
            "model": self.GROK_MODEL_NAME,
            "timeout": self.GROK_TIMEOUT,
        }

    def get_advanced_ai_config(self) -> dict:
        """获取高级分析AI的专属配置"""
        return {
            "api_keys": self.ADVANCED_AI_API_KEYS,
            "base_url": self.ADVANCED_AI_BASE_URL,
            "model": self.ADVANCED_AI_MODEL_NAME,
            "embedding_model": self.ADVANCED_AI_EMBEDDING_MODEL,
            "timeout": self.ADVANCED_AI_TIMEOUT,
        }

    def get_most_advanced_ai_config(self) -> dict:
        """获取最高级分析AI的专属配置"""
        return {
            "api_keys": self.MOST_ADVANCED_AI_API_KEYS,
            "base_url": self.MOST_ADVANCED_AI_BASE_URL,
            "model": self.MOST_ADVANCED_AI_MODEL_NAME,
            "timeout": self.MOST_ADVANCED_AI_TIMEOUT,
        }


settings = Settings()
